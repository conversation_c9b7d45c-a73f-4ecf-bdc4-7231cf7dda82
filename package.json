{"name": "landingpage-react-template", "private": true, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@iconify/react": "^6.0.0", "@next/bundle-analyzer": "^15.3.3", "@storybook/react": "^8.6.12", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.76.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^14.2.3", "@testing-library/user-event": "^7.1.2", "@vanilla-extract/css": "^1.17.1", "@vanilla-extract/vite-plugin": "^5.0.1", "antd": "^5.25.1", "apexcharts": "^4.7.0", "axios": "^1.8.6", "clsx": "^2.1.1", "color": "^5.0.0", "csurf": "^1.11.0", "dayjs": "^1.11.13", "depcheck": "1.4.7", "dompurify": "^3.2.6", "emailjs-com": "^2.6.4", "eslint-config-react-app": "^7.0.1", "framer-motion": "^12.9.7", "helmet": "^7.1.0", "highlight.js": "^11.11.1", "msw": "^2.8.2", "next": "^14.2.29", "prop-types": "^15.8.1", "ramda": "^0.30.1", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-number-format": "^5.4.4", "react-router": "^7.5.2", "react-router-dom": "^7.5.3", "react-toastify": "^11.0.5", "rollup-plugin-visualizer": "^5.14.0", "smooth-scroll": "^16.1.3", "sonner": "^2.0.3", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3", "zustand": "^5.0.3"}, "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "start": "PORT=3000 serve -s dist", "dev:next": "next dev -p 3011", "build:next": "next build", "start:next": "next start -p 3011", "export:next": "next build && next export", "docker:build": "docker build -t frost-chain-frontend:latest .", "docker:run": "docker run -d -p 80:80 -e NODE_ENV=production -e API_BASE_URL=/api -e APP_BASE_PATH=/ frost-chain-frontend:latest", "docker:build:local": "docker build -t frost-chain-frontend:local-test --build-arg NODE_ENV=development .", "docker:run:local": "docker run -d -p 8080:80 -e NODE_ENV=development -e API_BASE_URL=http://localhost:3000/api -e APP_BASE_PATH=/ -e USE_MOCK=true --name frost-chain-local-test frost-chain-frontend:local-test", "docker:local:up": "docker-compose -f docker-compose.local.yml up -d", "docker:local:down": "docker-compose -f docker-compose.local.yml down", "docker:local:logs": "docker-compose -f docker-compose.local.yml logs -f", "security:check": "bash scripts/security-check.sh", "security:audit": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "deps:check": "depcheck", "deps:update": "npm update", "build:secure": "bash scripts/secure-build.sh", "build:docker": "bash scripts/secure-build.sh --docker", "build:docker:push": "bash scripts/secure-build.sh --docker --push", "config:generate": "bash scripts/config-manager.sh generate-env", "config:validate": "bash scripts/config-manager.sh validate-config", "config:clean": "bash scripts/config-manager.sh clean-configs", "config:backup": "bash scripts/config-manager.sh backup-configs", "config:list": "bash scripts/config-manager.sh list-configs", "config:init": "bash scripts/config-manager.sh init-configs"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/dompurify": "^3.2.0", "@types/node": "^22.15.29", "@types/ramda": "^0.30.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-toastify": "^4.1.0", "@vitejs/plugin-react": "^4.4.1", "eslint-plugin-security": "^3.0.1", "less": "^4.3.0", "sass-embedded": "^1.87.0", "terser": "^5.39.2", "typescript": "^5.8.3", "vite": "^6.3.2", "vite-plugin-svgr": "^4.3.0"}, "type": "module"}