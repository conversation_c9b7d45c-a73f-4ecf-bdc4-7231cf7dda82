'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入原始的AppProviders，仅在客户端加载
const AppProviders = dynamic(
  () => import('@/components/providers/AppProviders').then(mod => ({ default: mod.AppProviders })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div>加载中...</div>
      </div>
    )
  }
);

interface SSRCompatibleProvidersProps {
  children: React.ReactNode;
}

/**
 * SSR兼容的提供者包装器
 * 严格保持原有AppProviders的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleProviders({ children }: SSRCompatibleProvidersProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示基础布局
  if (!isClient) {
    return (
      <div style={{ 
        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        lineHeight: 1.6,
        color: '#333'
      }}>
        {children}
      </div>
    );
  }

  // 客户端渲染时使用完整的AppProviders
  return (
    <AppProviders>
      {children}
    </AppProviders>
  );
}
