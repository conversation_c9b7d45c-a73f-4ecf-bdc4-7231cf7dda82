'use client';

// 保持现有的提供者组件导入 - 完全不变
import { AppProviders } from '@/components/providers/AppProviders';

// 保持现有的安全初始化 - 完全不变
import { SecurityInit } from '@/utils/securityInit';

// 保持现有的平滑滚动初始化 - 完全不变
import { useEffect } from 'react';
import SmoothScroll from "smooth-scroll";

interface ClientWrapperProps {
  children: React.ReactNode;
}

export function ClientWrapper({ children }: ClientWrapperProps) {
  // 保持现有的平滑滚动初始化逻辑 - 完全不变
  useEffect(() => {
    new SmoothScroll('a[href*="#"]', {
      speed: 800,
      speedAsDuration: true,
    });
  }, []);

  return (
    <>
      {/* 保持现有的提供者层级结构 - 完全不变 */}
      <AppProviders>
        {/* 保持现有的安全初始化 - 完全不变 */}
        <SecurityInit />
        
        {/* 子页面内容 */}
        {children}
      </AppProviders>
    </>
  );
}
