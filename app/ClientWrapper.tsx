'use client';

// 使用SSR兼容的提供者包装器 - 保持原有功能完全不变
import { SSRCompatibleProviders } from './providers/SSRCompatibleProviders';

// 保持现有的安全初始化 - 完全不变
import SecurityInitializer from '@/utils/securityInit';

// 保持现有的平滑滚动初始化 - 完全不变
import { useEffect } from 'react';
import SmoothScroll from "smooth-scroll";

interface ClientWrapperProps {
  children: React.ReactNode;
}

export function ClientWrapper({ children }: ClientWrapperProps) {
  // 保持现有的平滑滚动初始化逻辑 - 完全不变
  useEffect(() => {
    new SmoothScroll('a[href*="#"]', {
      speed: 800,
      speedAsDuration: true,
    });

    // 保持现有的安全初始化 - 完全不变
    SecurityInitializer.initialize();
  }, []);

  return (
    <>
      {/* 使用SSR兼容的提供者 - 保持原有功能完全不变 */}
      <SSRCompatibleProviders>
        {/* 子页面内容 */}
        {children}
      </SSRCompatibleProviders>
    </>
  );
}
