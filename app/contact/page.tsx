'use client';

import type { Metadata } from 'next';

// 使用SSR兼容的组件
import { SSRCompatibleNavigation } from '../components/SSRCompatibleNavigation';
import { Contact } from '@/features/contact';
import { CookieConsent } from '@/features/cookie-consent';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../components/NextScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

// metadata将通过layout.tsx处理

export default function ContactPage() {
  // 保持现有的数据获取逻辑 - 完全不变
  const data = JsonData;

  return (
    <>
      {/* 保持现有的MainLayout结构 - 完全不变 */}
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          {/* 保持现有的Contact组件完全不变 */}
          <Contact data={data.Contact} />
        </main>
        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
