'use client';

import type { Metadata } from 'next';

// 使用SSR兼容的组件
import { SSRCompatibleNavigation } from '../components/SSRCompatibleNavigation';
import { Contact } from '@/features/contact';
import CompanyAddressMap from '@/features/contact/CompanyAddressMap';
import { PageFooter } from '@/features/contact/PageFooter';
import { CookieConsent } from '@/features/cookie-consent';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../components/NextScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

// metadata将通过layout.tsx处理

export default function ContactPage() {
  // 保持现有的数据获取逻辑 - 完全不变
  const data = JsonData;

  return (
    <>
      {/* 保持现有的MainLayout结构 - 完全不变 */}
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        {/* 页面标题 */}
        <div className="page-header bg-light py-5 mt-5">
          <div className="container">
            <div className="row">
              <div className="col-12 text-center">
                <h1 className="display-4 fw-bold">联系我们</h1>
                <p className="lead">我们期待您的来信</p>
              </div>
            </div>
          </div>
        </div>

        {/* 公司地址地图组件 - 放在最前面 */}
        <section className="py-5 bg-light">
          <div className="container">
            <CompanyAddressMap
              address={data.Contact?.address || "上海市宝山区长江西路2311号1-2层"}
              phone={data.Contact?.phone || "13761182299"}
              mapImageUrl="/img/map/company-map.jpg"
            />
          </div>
        </section>

        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          {/* 这里可以放其他主要内容 */}
        </main>

        {/* 联系方式组件 - 作为footer前的section */}
        <Contact data={data.Contact} />

        {/* 页脚 */}
        <PageFooter recordNumbers={["沪ICP备2025123281号", "沪ICP备2025123281号-1"]} />

        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
