'use client';

import type { Metadata } from 'next';

// 保持现有的组件导入 - 完全不变
import { Navigation } from '@/features/navigation';
import { Header } from '@/features/header';
import { Gallery } from '@/features/gallery';
import { Contact } from '@/features/contact';
import { CookieConsent } from '@/features/cookie-consent';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { ScrollToTop } from '@/components/utility/ScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

// metadata将通过layout.tsx处理

// 服务端数据获取 - 保持现有数据结构
async function getData() {
  try {
    return JsonData;
  } catch (error) {
    console.error('Failed to fetch gallery data:', error);
    return {
      Header: {},
      Gallery: {},
      Contact: {},
    };
  }
}

export default async function GalleryPage() {
  const data = await getData();

  return (
    <>
      {/* 保持现有的MainLayout结构 - 完全不变 */}
      <div className="d-flex flex-column min-vh-100">
        <ScrollToTop />
        <Navigation />
        <Header data={data.Header} />
        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          {/* 保持现有的Gallery组件完全不变 */}
          <Gallery data={data.Gallery} />
        </main>
        <Contact data={data.Contact} />
        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
