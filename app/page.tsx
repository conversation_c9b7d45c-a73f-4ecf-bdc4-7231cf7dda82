'use client';

import type { Metadata } from 'next';

// 保持现有的组件导入 - 完全不变
import { Navigation } from '@/features/navigation';
import { Header } from '@/features/header';
import { Gallery } from '@/features/gallery';
import { Features } from '@/features/feature';
import { About } from '@/features/about/about';
import { Services } from '@/features/services';
import { Contact } from '@/features/contact';
import { CookieConsent } from '@/features/cookie-consent';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from './components/NextScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

// metadata将通过layout.tsx处理

// 服务端数据获取（保持现有数据结构）
async function getData() {
  try {
    // 保持现有的数据获取逻辑 - 完全不变
    return JsonData;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    // 返回默认数据，保持现有的错误处理 - 完全不变
    return {
      Header: {},
      Gallery: {},
      Features: {},
      About: {},
      Services: {},
      Contact: {},
    };
  }
}

export default async function HomePage() {
  const data = await getData();

  return (
    <>
      {/* 保持现有的布局结构 - 完全不变 */}
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <Navigation />
        <Header data={data.Header} />
        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          {/* 保持现有的首页组件结构 - 完全不变 */}
          <Gallery data={data.Gallery} />
          <Features data={data.Features} />
          <About data={data.About} />
          <Services data={data.Services} />
          {/* 保持注释状态 - 完全不变 */}
          {/* <Testimonials data={data.Testimonials} />*/}
          {/* <Team data={data.Team} />*/}
        </main>
        <Contact data={data.Contact} />
        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
