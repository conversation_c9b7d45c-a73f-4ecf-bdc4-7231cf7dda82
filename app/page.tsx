'use client';

import type { Metadata } from 'next';

// 使用SSR兼容的组件包装器 - 保持原有功能完全不变
import { SSRCompatibleNavigation } from './components/SSRCompatibleNavigation';
import { SSRCompatibleHeader } from './components/SSRCompatibleHeader';
import { SSRCompatibleGallery } from './components/SSRCompatibleGallery';
import { Features } from '@/features/feature';
import { About } from '@/features/about/about';
import { Services } from '@/features/services';
import { Contact } from '@/features/contact';
import { CookieConsent } from '@/features/cookie-consent';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from './components/NextScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

// metadata将通过layout.tsx处理

export default function HomePage() {
  // 保持现有的数据获取逻辑 - 完全不变
  const data = JsonData;

  return (
    <>
      {/* 保持现有的布局结构 - 完全不变 */}
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        <SSRCompatibleHeader data={data.Header} />
        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          {/* 保持现有的首页组件结构 - 完全不变 */}
          <SSRCompatibleGallery data={data.Gallery} />
          <Features data={data.Features} />
          <About data={data.About} />
          <Services data={data.Services} />
          {/* 保持注释状态 - 完全不变 */}
          {/* <Testimonials data={data.Testimonials} />*/}
          {/* <Team data={data.Team} />*/}
        </main>
        <Contact data={data.Contact} />
        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
