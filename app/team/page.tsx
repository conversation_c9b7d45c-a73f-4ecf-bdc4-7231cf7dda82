import type { Metadata } from 'next';

// 保持现有的组件导入 - 完全不变
import { Navigation } from '@/features/navigation';
import { Team } from '@/features/team';
import { PageFooter } from '@/components/layout/PageFooter';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { ScrollToTop } from '@/components/utility/ScrollToTop';

// 保持现有的数据获取 - 完全不变
import JsonData from '@/data/data.json';

export const metadata: Metadata = {
  title: '团队介绍 - 寒链',
  description: '了解寒链的专业团队成员和企业文化',
  openGraph: {
    title: '团队介绍 - 寒链',
    description: '了解寒链的专业团队成员和企业文化',
  },
};

// 服务端数据获取 - 保持现有数据结构
async function getData() {
  try {
    return JsonData;
  } catch (error) {
    console.error('Failed to fetch team data:', error);
    return {
      Team: {},
    };
  }
}

export default async function TeamPage() {
  const data = await getData();

  // 保持现有的TeamLayoutNew配置 - 完全不变
  const teamLayoutConfig = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
      recordNumbers: ["沪ICP备2025123281号", "沪ICP备2025123281号-1"]
    },
    contactFloatBarConfig: {
      items: defaultContactItems,
      themeColor: "#1677ff",
      top: "50%"
    }
  };

  return (
    <>
      {/* 保持现有的TeamLayoutNew结构 - 完全不变 */}
      <div className={teamLayoutConfig.containerClassName}>
        <ScrollToTop />
        
        <Navigation />
        
        <main 
          id="main-content" 
          tabIndex={-1} 
          className={teamLayoutConfig.mainClassName}
        >
          <ErrorBoundary>
            {/* 保持现有的Team组件完全不变 */}
            <Team data={data.Team} />
          </ErrorBoundary>
        </main>
        
        <PageFooter {...teamLayoutConfig.footerConfig} />
        
        <ContactFloatBar {...teamLayoutConfig.contactFloatBarConfig} />
      </div>
    </>
  );
}
