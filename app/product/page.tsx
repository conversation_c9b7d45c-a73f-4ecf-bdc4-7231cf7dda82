'use client';

import type { Metadata } from 'next';

// 使用SSR兼容的导航组件
import { SSRCompatibleNavigation } from '../components/SSRCompatibleNavigation';
import ProductPage from '@/features/product';
import { PageFooter } from '@/features/contact/PageFooter';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../components/NextScrollToTop';
import { ApiProvider } from '@/services/api/ApiProvider';
import { QueryProvider } from '@/services/query/QueryProvider';
import { HelmetProvider } from 'react-helmet-async';

// metadata将通过layout.tsx处理

export default function ProductPageRoute() {
  // 保持现有的ProductLayout配置 - 完全不变
  const productLayoutConfig = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
      recordNumbers: ["沪ICP备**********号", "沪ICP备**********号-1"]
    },
    contactFloatBarConfig: {
      items: defaultContactItems,
      themeColor: "#52c41a",
      top: "45%"
    }
  };

  return (
    <HelmetProvider>
      <ApiProvider>
        <QueryProvider>
          {/* 保持现有的ProductLayout结构 - 完全不变 */}
          <div className={productLayoutConfig.containerClassName}>
            <NextScrollToTop />

            <SSRCompatibleNavigation />

            <main
              id="main-content"
              tabIndex={-1}
              className={productLayoutConfig.mainClassName}
            >
              <ErrorBoundary>
                {/* 保持现有的ProductPage组件完全不变 */}
                <ProductPage />
              </ErrorBoundary>
            </main>

            <PageFooter {...productLayoutConfig.footerConfig} />

            <ContactFloatBar {...productLayoutConfig.contactFloatBarConfig} />
          </div>
        </QueryProvider>
      </ApiProvider>
    </HelmetProvider>
  );
}
