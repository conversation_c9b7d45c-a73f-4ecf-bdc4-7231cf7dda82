import type { Metadata } from 'next';

// 保持现有的组件导入 - 完全不变
import { Navigation } from '@/features/navigation';
import ProductPage from '@/features/product';
import { PageFooter } from '@/components/layout/PageFooter';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { ScrollToTop } from '@/components/utility/ScrollToTop';

export const metadata: Metadata = {
  title: '产品信息中心 - 寒链',
  description: '上海寒链实业有限公司提供高品质工业冰和食用冰产品，满足各种工业和商业需求',
  keywords: '工业冰产品,食用冰产品,冷链服务,上海寒链',
  openGraph: {
    title: '产品信息中心 - 寒链',
    description: '上海寒链实业有限公司提供高品质工业冰和食用冰产品',
  },
};

export default function ProductPageRoute() {
  // 保持现有的ProductLayout配置 - 完全不变
  const productLayoutConfig = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
      recordNumbers: ["沪ICP备2025123281号", "沪ICP备2025123281号-1"]
    },
    contactFloatBarConfig: {
      items: defaultContactItems,
      themeColor: "#52c41a",
      top: "45%"
    }
  };

  return (
    <>
      {/* 保持现有的ProductLayout结构 - 完全不变 */}
      <div className={productLayoutConfig.containerClassName}>
        <ScrollToTop />
        
        <Navigation />
        
        <main 
          id="main-content" 
          tabIndex={-1} 
          className={productLayoutConfig.mainClassName}
        >
          <ErrorBoundary>
            {/* 保持现有的ProductPage组件完全不变 */}
            <ProductPage />
          </ErrorBoundary>
        </main>
        
        <PageFooter {...productLayoutConfig.footerConfig} />
        
        <ContactFloatBar {...productLayoutConfig.contactFloatBarConfig} />
      </div>
    </>
  );
}
