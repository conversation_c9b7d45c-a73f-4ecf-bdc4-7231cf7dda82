'use client';

import { useParams, useRouter } from 'next/navigation';
import { SSRCompatibleNavigation } from '../../components/SSRCompatibleNavigation';
import { PageFooter } from '@/features/contact/PageFooter';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../../components/NextScrollToTop';
import { HelmetProvider } from 'react-helmet-async';

// 产品数据接口
interface ProductItem {
  id: string;
  title: string;
  category: string;
  description: string;
  features: string[];
  specifications: { [key: string]: string };
  images: string[];
  price?: string;
  applications: string[];
}

// 模拟产品数据
const productData: ProductItem[] = [
  {
    id: '1',
    title: '工业冰块',
    category: '工业冰产品',
    description: '专为工业应用设计的高品质冰块，具有优异的冷却性能和持久的保冷效果。适用于各种工业设备的冷却需求。',
    features: [
      '高纯度制冰工艺',
      '持久保冷效果',
      '规格可定制',
      '快速冷却能力',
      '环保无污染'
    ],
    specifications: {
      '规格': '10kg/块、20kg/块、50kg/块',
      '温度': '-18°C至-25°C',
      '保存时间': '24-48小时',
      '纯度': '99.9%',
      '包装': '食品级包装材料'
    },
    images: [
      '/img/industrial-ice-1.jpg',
      '/img/industrial-ice-2.jpg',
      '/img/industrial-ice-3.jpg'
    ],
    applications: [
      '化工设备冷却',
      '机械设备降温',
      '混凝土降温',
      '食品加工冷却',
      '医药设备冷却'
    ]
  },
  {
    id: '2',
    title: '食用冰块',
    category: '食用冰产品',
    description: '严格按照食品安全标准生产的食用级冰块，纯净卫生，适用于餐饮、饮品等食品相关应用。',
    features: [
      '食品级安全标准',
      '纯净水源制作',
      '无菌生产环境',
      '快速冷冻技术',
      '多种规格可选'
    ],
    specifications: {
      '规格': '2kg/袋、5kg/袋、10kg/袋',
      '温度': '-18°C',
      '保存时间': '12-24小时',
      '纯度': '99.99%',
      '认证': 'HACCP、ISO22000'
    },
    images: [
      '/img/food-ice-1.jpg',
      '/img/food-ice-2.jpg',
      '/img/food-ice-3.jpg'
    ],
    applications: [
      '餐厅饮品制作',
      '海鲜保鲜',
      '食品展示',
      '酒店服务',
      '活动餐饮'
    ]
  },
  {
    id: '3',
    title: '干冰产品',
    category: '特殊冰产品',
    description: '高品质干冰产品，具有极低温度和升华特性，适用于特殊冷却、保鲜和舞台效果等应用。',
    features: [
      '极低温冷却',
      '无残留升华',
      '环保无毒',
      '冷却效果持久',
      '多种形态可选'
    ],
    specifications: {
      '规格': '颗粒状、块状、片状',
      '温度': '-78.5°C',
      '纯度': '99.9%',
      '包装': '专用保温箱',
      '升华时间': '根据环境温度而定'
    },
    images: [
      '/img/dry-ice-1.jpg',
      '/img/dry-ice-2.jpg',
      '/img/dry-ice-3.jpg'
    ],
    applications: [
      '冷链运输',
      '医药保存',
      '舞台效果',
      '清洁应用',
      '科研实验'
    ]
  },
  {
    id: '4',
    title: '冰雕定制',
    category: '冰雕服务',
    description: '专业冰雕艺术定制服务，为各种活动和场合提供精美的冰雕作品，展现独特的艺术魅力。',
    features: [
      '专业艺术设计',
      '精湛雕刻工艺',
      '个性化定制',
      '现场制作服务',
      '完善售后保障'
    ],
    specifications: {
      '材料': '优质透明冰块',
      '尺寸': '根据需求定制',
      '制作周期': '3-7个工作日',
      '保存时间': '室温下2-6小时',
      '运输': '专业冷链配送'
    },
    images: [
      '/img/ice-sculpture-1.jpg',
      '/img/ice-sculpture-2.jpg',
      '/img/ice-sculpture-3.jpg'
    ],
    applications: [
      '婚礼庆典',
      '商业活动',
      '酒店装饰',
      '艺术展览',
      '节庆活动'
    ]
  },
  {
    id: '5',
    title: '冰块配送',
    category: '配送服务',
    description: '专业的冰块配送服务，提供及时、可靠的冷链配送，确保冰块在最佳状态下送达客户手中。',
    features: [
      '24小时配送服务',
      '专业冷链车辆',
      'GPS实时跟踪',
      '温度监控系统',
      '灵活配送时间'
    ],
    specifications: {
      '配送范围': '市区及周边地区',
      '配送时间': '24小时内',
      '最小订量': '50kg起',
      '车辆': '专业冷藏车',
      '温控': '全程-18°C'
    },
    images: [
      '/img/ice-delivery-1.jpg',
      '/img/ice-delivery-2.jpg',
      '/img/ice-delivery-3.jpg'
    ],
    applications: [
      '餐饮企业配送',
      '酒店定期供应',
      '活动现场配送',
      '工厂设备冷却',
      '紧急冷却需求'
    ]
  }
];

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params?.id as string;
  
  // 查找对应的产品
  const product = productData.find(item => item.id === id);

  // 返回按钮处理
  const handleBack = () => {
    router.push('/product');
  };

  // 如果产品不存在
  if (!product) {
    return (
      <HelmetProvider>
        <div className="d-flex flex-column min-vh-100">
          <NextScrollToTop />
          <SSRCompatibleNavigation />
          
          <main id="main-content" tabIndex={-1} className="flex-grow-1 mt-5 pt-4">
            <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
              <div style={{ 
                textAlign: 'center', 
                padding: '4rem 2rem',
                color: '#666',
                fontSize: '1.2rem'
              }}>
                <h1 style={{ color: '#333', marginBottom: '1rem' }}>产品不存在</h1>
                <p>抱歉，您访问的产品页面不存在。</p>
                <button
                  onClick={handleBack}
                  style={{
                    marginTop: '2rem',
                    padding: '0.75rem 2rem',
                    backgroundColor: '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '1rem'
                  }}
                >
                  返回产品列表
                </button>
              </div>
            </div>
          </main>

          <PageFooter recordNumbers={["沪ICP备**********号", "沪ICP备**********号-1"]} />
          <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
        </div>
      </HelmetProvider>
    );
  }

  return (
    <HelmetProvider>
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        
        <main id="main-content" tabIndex={-1} className="flex-grow-1 mt-5 pt-4">
          <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
            {/* 返回按钮 */}
            <button
              onClick={handleBack}
              style={{
                marginBottom: '2rem',
                padding: '0.5rem 1rem',
                backgroundColor: '#f0f0f0',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.9rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              ← 返回产品列表
            </button>

            {/* 产品标题 */}
            <h1 style={{ 
              textAlign: 'center', 
              marginBottom: '2rem', 
              color: '#333', 
              fontSize: '2rem' 
            }}>
              {product.title}
            </h1>

            {/* 产品内容 */}
            <div className="row">
              {/* 左侧：产品图片 */}
              <div className="col-md-6">
                <div style={{
                  width: '100%',
                  height: '400px',
                  overflow: 'hidden',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  marginBottom: '1rem'
                }}>
                  <img 
                    src={product.images[0]} 
                    alt={product.title}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      e.currentTarget.src = '/img/placeholder-product.jpg';
                    }}
                  />
                </div>
                
                {/* 产品图片缩略图 */}
                {product.images.length > 1 && (
                  <div style={{
                    display: 'flex',
                    gap: '0.5rem',
                    overflowX: 'auto'
                  }}>
                    {product.images.map((image, index) => (
                      <div
                        key={index}
                        style={{
                          width: '80px',
                          height: '80px',
                          overflow: 'hidden',
                          borderRadius: '4px',
                          border: '2px solid #e0e0e0',
                          cursor: 'pointer',
                          flexShrink: 0
                        }}
                      >
                        <img 
                          src={image} 
                          alt={`${product.title} ${index + 1}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                          onError={(e) => {
                            e.currentTarget.src = '/img/placeholder-product.jpg';
                          }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 右侧：产品信息 */}
              <div className="col-md-6">
                <div style={{
                  background: 'white',
                  padding: '2rem',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  height: 'fit-content'
                }}>
                  <h2 style={{ 
                    marginTop: 0, 
                    color: '#333', 
                    fontSize: '1.5rem',
                    marginBottom: '1rem'
                  }}>
                    {product.title}
                  </h2>
                  
                  <p style={{ 
                    margin: '1rem 0', 
                    color: '#666', 
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>
                    分类: {product.category}
                  </p>
                  
                  <p style={{ 
                    margin: '1rem 0', 
                    color: '#555', 
                    lineHeight: 1.6,
                    fontSize: '0.95rem'
                  }}>
                    {product.description}
                  </p>

                  {/* 产品特点 */}
                  <div style={{ marginTop: '2rem' }}>
                    <h3 style={{ 
                      color: '#333', 
                      fontSize: '1.2rem',
                      marginBottom: '1rem'
                    }}>
                      产品特点
                    </h3>
                    <ul style={{ 
                      paddingLeft: '1.5rem',
                      color: '#555',
                      lineHeight: 1.6
                    }}>
                      {product.features.map((feature, index) => (
                        <li key={index} style={{ marginBottom: '0.5rem' }}>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* 应用场景 */}
                  <div style={{ marginTop: '2rem' }}>
                    <h3 style={{ 
                      color: '#333', 
                      fontSize: '1.2rem',
                      marginBottom: '1rem'
                    }}>
                      应用场景
                    </h3>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.5rem'
                    }}>
                      {product.applications.map((app, index) => (
                        <span
                          key={index}
                          style={{
                            padding: '0.25rem 0.75rem',
                            backgroundColor: '#f0f0f0',
                            borderRadius: '12px',
                            fontSize: '0.8rem',
                            color: '#666'
                          }}
                        >
                          {app}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 产品规格 */}
            <div style={{ marginTop: '3rem' }}>
              <h3 style={{ 
                color: '#333', 
                fontSize: '1.3rem',
                marginBottom: '1rem'
              }}>
                产品规格
              </h3>
              <div style={{
                background: 'white',
                padding: '2rem',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
              }}>
                <div className="row">
                  {Object.entries(product.specifications).map(([key, value], index) => (
                    <div key={index} className="col-md-6" style={{ marginBottom: '1rem' }}>
                      <div style={{
                        padding: '1rem',
                        border: '1px solid #e0e0e0',
                        borderRadius: '4px'
                      }}>
                        <strong style={{ color: '#333' }}>{key}:</strong>
                        <span style={{ color: '#555', marginLeft: '0.5rem' }}>{value}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>

        <PageFooter recordNumbers={["沪ICP备**********号", "沪ICP备**********号-1"]} />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </HelmetProvider>
  );
}
