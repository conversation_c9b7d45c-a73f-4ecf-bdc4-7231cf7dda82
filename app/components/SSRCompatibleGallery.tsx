'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入原始的Gallery组件，仅在客户端加载
const OriginalGallery = dynamic(
  () => import('@/features/gallery/Gallery').then(mod => ({ default: mod.Gallery })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <section style={{ 
        padding: '4rem 0',
        background: '#f8f9fa',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ 
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          padding: '0 1rem'
        }}>
          <h2 style={{ 
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            color: '#333'
          }}>
            产品展示
          </h2>
          <p style={{ 
            fontSize: '1.1rem',
            color: '#666',
            marginBottom: '3rem'
          }}>
            加载中...
          </p>
          <div style={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginTop: '2rem'
          }}>
            {[1, 2, 3].map(i => (
              <div key={i} style={{
                background: '#fff',
                borderRadius: '8px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                height: '200px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999'
              }}>
                加载中...
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }
);

interface SSRCompatibleGalleryProps {
  data?: any;
}

/**
 * SSR兼容的Gallery包装器
 * 严格保持原有Gallery的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleGallery({ data }: SSRCompatibleGalleryProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示与原始Gallery完全相同的结构
  if (!isClient) {
    // 产品数据 - 与原始Gallery组件完全相同
    const productData = [
      {
        id: 'industrial-ice',
        title: '工业冰',
        image: '/img/industrial-ice-1.jpg',
        description: '专为工业应用设计的高质量冰块，适用于建筑、混凝土养护、食品加工等领域。我们的工业冰具有更长的融化时间和更高的冷却效率，帮助您降低成本并提高生产效率。',
        features: ['高冷却效率', '长时间保持低温', '定制尺寸', '大批量供应']
      },
      {
        id: 'edible-ice',
        title: '食用冰',
        image: '/img/food-ice-1.jpg',
        description: '符合食品安全标准的高品质食用冰，适用于餐饮、酒店、超市等场所。我们的食用冰采用纯净水制作，确保安全卫生，为您的饮品和食品提供完美的冷却解决方案。',
        features: ['食品级安全标准', '晶莹剔透', '多种形状可选', '快速配送服务']
      },
      {
        id: 'cooling-ice',
        title: '降温冰',
        image: '/img/dry-ice-1.jpg',
        description: '专为环境降温设计的特殊冰块，适用于户外活动、工地降温、仓库温控等场景。我们的降温冰融化速度适中，能够持续释放冷量，有效改善高温环境。',
        features: ['高效降温', '环保无污染', '适用多种场景', '成本效益高']
      }
    ];

    return (
      <section
        id="portfolio"
        style={{
          padding: '60px 0',
          backgroundColor: '#f7f9fc'
        }}
      >
        <div className="container">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '40px', justifyContent: 'center' }}>
            <div style={{ textAlign: 'center' }}>
              <h2 style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                marginBottom: '16px',
                color: '#000000d9'
              }}>
                产品图库
              </h2>
              <p style={{
                fontSize: '16px',
                maxWidth: '800px',
                margin: '0 auto',
                color: '#00000073'
              }}>
                上海寒链实业有限公司提供多种高品质冰产品，满足不同行业和场景的需求。
                从工业应用到食品服务，我们都能提供专业的冷链解决方案。
              </p>
              <hr style={{
                width: '80px',
                minWidth: '80px',
                margin: '24px auto',
                border: 'none',
                borderTop: '1px solid #d9d9d9'
              }} />
            </div>

            <div>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '24px'
              }}>
                {productData.map(product => (
                  <div
                    key={product.id}
                    style={{
                      height: '100%',
                      transition: 'all 0.3s ease',
                      overflow: 'hidden',
                      backgroundColor: '#fff',
                      borderRadius: '8px',
                      border: '1px solid #f0f0f0',
                      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)'
                    }}
                  >
                    <div style={{ overflow: 'hidden' }}>
                      <img
                        alt={product.title}
                        src={product.image}
                        style={{
                          height: '240px',
                          width: '100%',
                          objectFit: 'cover',
                          transition: 'transform 0.5s ease'
                        }}
                      />
                    </div>
                    <div style={{ padding: '24px' }}>
                      <div style={{ marginBottom: '16px' }}>
                        <div style={{
                          fontSize: '20px',
                          marginBottom: '12px',
                          fontWeight: 500,
                          color: '#000000d9'
                        }}>
                          {product.title}
                        </div>
                        <div style={{
                          height: '100px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 4,
                          WebkitBoxOrient: 'vertical',
                          color: '#00000073'
                        }}>
                          {product.description}
                        </div>
                      </div>
                      <div style={{ marginTop: '16px' }}>
                        {product.features.map((feature, index) => (
                          <span
                            key={index}
                            style={{
                              display: 'inline-block',
                              backgroundColor: '#f0f5ff',
                              color: '#1890ff',
                              padding: '4px 10px',
                              borderRadius: '12px',
                              fontSize: '12px',
                              marginRight: '8px',
                              marginBottom: '8px'
                            }}
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div style={{
                      borderTop: '1px solid #f0f0f0',
                      padding: '16px 24px',
                      textAlign: 'center'
                    }}>
                      <a
                        href={`/product?category=${product.id}`}
                        style={{
                          color: '#1890ff',
                          textDecoration: 'none',
                          fontSize: '14px'
                        }}
                      >
                        <span style={{ marginRight: '8px' }}>👁</span>
                        查看详情
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <a
                href="/product"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  marginTop: '40px',
                  height: '48px',
                  fontSize: '16px',
                  padding: '0 32px',
                  backgroundColor: '#1890ff',
                  color: '#fff',
                  border: 'none',
                  borderRadius: '6px',
                  textDecoration: 'none',
                  cursor: 'pointer'
                }}
              >
                查看更多产品信息
                <span style={{ marginLeft: '8px' }}>→</span>
              </a>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // 客户端渲染时使用完整的Gallery组件
  return <OriginalGallery data={data} />;
}
