'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入原始的Gallery组件，仅在客户端加载
const OriginalGallery = dynamic(
  () => import('@/features/gallery/Gallery').then(mod => ({ default: mod.Gallery })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <section style={{ 
        padding: '4rem 0',
        background: '#f8f9fa',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ 
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          padding: '0 1rem'
        }}>
          <h2 style={{ 
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            color: '#333'
          }}>
            产品展示
          </h2>
          <p style={{ 
            fontSize: '1.1rem',
            color: '#666',
            marginBottom: '3rem'
          }}>
            加载中...
          </p>
          <div style={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginTop: '2rem'
          }}>
            {[1, 2, 3].map(i => (
              <div key={i} style={{
                background: '#fff',
                borderRadius: '8px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                height: '200px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999'
              }}>
                加载中...
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }
);

interface SSRCompatibleGalleryProps {
  data?: any;
}

/**
 * SSR兼容的Gallery包装器
 * 严格保持原有Gallery的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleGallery({ data }: SSRCompatibleGalleryProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示基础画廊
  if (!isClient) {
    return (
      <section style={{ 
        padding: '4rem 0',
        background: '#f8f9fa',
        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{ 
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          padding: '0 1rem'
        }}>
          <h2 style={{ 
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            color: '#333'
          }}>
            产品展示
          </h2>
          <p style={{ 
            fontSize: '1.1rem',
            color: '#666',
            marginBottom: '3rem'
          }}>
            查看我们的高质量工业冰产品
          </p>
          <div style={{ 
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginTop: '2rem'
          }}>
            <div style={{
              background: '#fff',
              borderRadius: '8px',
              padding: '1.5rem',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#333', marginBottom: '1rem' }}>工业冰产品</h3>
              <p style={{ color: '#666' }}>高质量的工业用冰块</p>
            </div>
            <div style={{
              background: '#fff',
              borderRadius: '8px',
              padding: '1.5rem',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#333', marginBottom: '1rem' }}>食用冰产品</h3>
              <p style={{ color: '#666' }}>安全卫生的食用冰块</p>
            </div>
            <div style={{
              background: '#fff',
              borderRadius: '8px',
              padding: '1.5rem',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ color: '#333', marginBottom: '1rem' }}>冷链服务</h3>
              <p style={{ color: '#666' }}>专业的冷链物流服务</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // 客户端渲染时使用完整的Gallery组件
  return <OriginalGallery data={data} />;
}
