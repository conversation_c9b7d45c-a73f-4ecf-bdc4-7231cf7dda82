'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入原始的Header组件，仅在客户端加载
const OriginalHeader = dynamic(
  () => import('@/features/header/Header').then(mod => ({ default: mod.Header })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => null // 不显示加载状态，避免布局闪烁
  }
);

interface SSRCompatibleHeaderProps {
  data?: any;
}

/**
 * SSR兼容的Header包装器
 * 严格保持原有Header的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleHeader({ data }: SSRCompatibleHeaderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示与原始Header完全相同的结构，但不包含交互功能
  if (!isClient) {
    const metaDescription = data?.paragraph || "上海寒链实业有限公司专注工业制造与创新科技";
    const pageTitle = data?.title || "上海寒链实业有限公司";

    // 轮播数据 - 与原始组件完全相同
    const carouselItems = [
      {
        id: 1,
        webp: "/img/background1.webp",
        fallback: "/img/intro-bg.jpg",
        alt: "工业冰",
        title: "工业冰",
        description: "工业冰",
        loading: "eager"
      },
      {
        id: 2,
        webp: "/img/background2.webp",
        fallback: "/img/industry-bg.jpg",
        alt: "食用冰",
        title: "食用冰",
        description: "食用冰",
        loading: "lazy"
      },
      {
        id: 3,
        webp: "/img/background3.webp",
        fallback: "/img/tech-bg.jpg",
        alt: "创新科技研发",
        title: "创新技术研发",
        loading: "lazy"
      }
    ];

    return (
      <header id="header" role="banner">
        {/* 服务端渲染时显示与AntCarousel完全相同的结构 */}
        <div className="ant-carousel-container">
          <div className="ant-carousel ant-carousel-fade">
            <div className="slick-slider slick-initialized">
              <div className="slick-list">
                <div className="slick-track" style={{ opacity: 1, transform: 'translate3d(0px, 0px, 0px)' }}>
                  {carouselItems.map((item, index) => (
                    <div
                      key={item.id}
                      className={`slick-slide ${index === 0 ? 'slick-active slick-current' : ''}`}
                      style={{ width: '100%', position: 'relative' }}
                    >
                      <div className="carousel-slide">
                        <div className="slide-content">
                          <picture>
                            <source srcSet={item.webp} type="image/webp" />
                            <img
                              src={item.fallback}
                              alt={item.alt}
                              className="slide-image"
                              loading={item.loading as "eager" | "lazy"}
                              decoding="async"
                            />
                          </picture>
                          <div className="slide-caption">
                            {/* 可以在这里添加按钮，如果需要的话 */}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              {/* 轮播指示器 */}
              <ul className="slick-dots slick-dots-bottom">
                {carouselItems.map((_, index) => (
                  <li key={index} className={index === 0 ? 'slick-active' : ''}>
                    <button type="button">{index + 1}</button>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </header>
    );
  }

  // 客户端渲染时使用完整的Header组件
  return <OriginalHeader data={data} />;
}
