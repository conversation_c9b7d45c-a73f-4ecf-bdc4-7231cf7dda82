'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入AntCarousel组件，避免React Helmet冲突
const AntCarousel = dynamic(
  () => import('@/components/display/Carousel').then(mod => ({ default: mod.AntCarousel })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => null
  }
);

interface SSRCompatibleHeaderProps {
  data?: any;
}

/**
 * SSR兼容的Header包装器
 * 严格保持原有Header的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleHeader({ data }: SSRCompatibleHeaderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示占位符，避免布局跳动
  if (!isClient) {
    return (
      <header id="header" role="banner">
        <div
          className="ant-carousel-container"
          style={{
            width: '100%',
            height: '500px',
            backgroundColor: '#f8f9fa',
            backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* 背景图片 */}
          <img
            src="/img/intro-bg.jpg"
            alt="工业冰"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center',
              zIndex: 1
            }}
          />

          {/* 覆盖层 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              zIndex: 2
            }}
          />

          {/* 内容层 */}
          <div
            style={{
              position: 'relative',
              zIndex: 3,
              textAlign: 'center',
              color: 'white',
              padding: '20px'
            }}
          >
            <h1 style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              marginBottom: '1rem',
              textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
            }}>
              上海寒链实业有限公司
            </h1>
            <p style={{
              fontSize: '1.2rem',
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
            }}>
              专业的工业冰制造和供应商
            </p>
          </div>

          {/* 轮播指示器 */}
          <div style={{
            position: 'absolute',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: '8px',
            zIndex: 3
          }}>
            {[0, 1, 2].map((index) => (
              <div
                key={index}
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: index === 0 ? 'white' : 'rgba(255, 255, 255, 0.5)',
                  cursor: 'pointer'
                }}
              />
            ))}
          </div>
        </div>
      </header>
    );
  }

  // 客户端渲染时使用完整的Header组件
  return <OriginalHeader data={data} />;
}
