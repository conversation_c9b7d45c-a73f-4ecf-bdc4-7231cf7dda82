'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 动态导入原始的Header组件，仅在客户端加载
const OriginalHeader = dynamic(
  () => import('@/features/header/Header').then(mod => ({ default: mod.Header })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <header style={{ 
        height: '80px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div>加载中...</div>
      </header>
    )
  }
);

interface SSRCompatibleHeaderProps {
  data?: any;
}

/**
 * SSR兼容的Header包装器
 * 严格保持原有Header的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleHeader({ data }: SSRCompatibleHeaderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时返回null，避免样式不一致
  if (!isClient) {
    return null;
  }

  // 客户端渲染时使用完整的Header组件
  return <OriginalHeader data={data} />;
}
