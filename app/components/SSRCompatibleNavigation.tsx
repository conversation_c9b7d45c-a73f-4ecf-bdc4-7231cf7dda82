'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';

// 动态导入原始的Navigation组件，仅在客户端加载
const OriginalNavigation = dynamic(
  () => import('@/features/navigation/Navigation').then(mod => ({ default: mod.Navigation })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <nav style={{ 
        background: '#fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '1rem 0',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ 
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 1rem'
        }}>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#333' }}>
            寒链
          </div>
          <div style={{ display: 'flex', gap: '2rem' }}>
            <Link href="/" style={{ textDecoration: 'none', color: '#333' }}>首页</Link>
            <Link href="/about" style={{ textDecoration: 'none', color: '#333' }}>关于</Link>
            <Link href="/services" style={{ textDecoration: 'none', color: '#333' }}>服务</Link>
            <Link href="/contact" style={{ textDecoration: 'none', color: '#333' }}>联系</Link>
          </div>
        </div>
      </nav>
    )
  }
);

/**
 * SSR兼容的Navigation包装器
 * 严格保持原有Navigation的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleNavigation() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示与原始Navigation完全相同的结构
  if (!isClient) {
    return (
      <nav className="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div className="container">
          <a className="navbar-brand" href="#page-top">
            寒链
          </a>
          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarCollapse"
            aria-controls="navbarCollapse"
            aria-expanded="false"
            aria-label="Toggle navigation"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarCollapse">
            <ul className="navbar-nav ms-auto mb-2 mb-lg-0">
              <li className="nav-item">
                <Link href="/" className="nav-link">首页</Link>
              </li>
              <li className="nav-item">
                <Link href="/product" className="nav-link">产品信息</Link>
              </li>
              <li className="nav-item">
                <Link href="/cases" className="nav-link">客户案例</Link>
              </li>
              <li className="nav-item">
                <Link href="/team" className="nav-link">团队</Link>
              </li>
              <li className="nav-item">
                <Link href="/contact" className="nav-link">联系我们</Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    );
  }

  // 客户端渲染时使用完整的Navigation组件
  return <OriginalNavigation />;
}
