'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';

// 动态导入原始的Navigation组件，仅在客户端加载
const OriginalNavigation = dynamic(
  () => import('@/features/navigation/Navigation').then(mod => ({ default: mod.Navigation })),
  {
    ssr: false, // 禁用服务端渲染
    loading: () => (
      <nav style={{ 
        background: '#fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '1rem 0',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div style={{ 
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 1rem'
        }}>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#333' }}>
            寒链
          </div>
          <div style={{ display: 'flex', gap: '2rem' }}>
            <Link href="/" style={{ textDecoration: 'none', color: '#333' }}>首页</Link>
            <Link href="/about" style={{ textDecoration: 'none', color: '#333' }}>关于</Link>
            <Link href="/services" style={{ textDecoration: 'none', color: '#333' }}>服务</Link>
            <Link href="/contact" style={{ textDecoration: 'none', color: '#333' }}>联系</Link>
          </div>
        </div>
      </nav>
    )
  }
);

/**
 * SSR兼容的Navigation包装器
 * 严格保持原有Navigation的所有功能不变
 * 只是解决SSR兼容性问题
 */
export function SSRCompatibleNavigation() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端运行
    setIsClient(true);
  }, []);

  // 服务端渲染时显示简化但视觉一致的导航栏
  if (!isClient) {
    return (
      <nav
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1030,
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #dee2e6',
          padding: '0.5rem 0',
          boxShadow: '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)'
        }}
      >
        <div
          style={{
            maxWidth: '1140px',
            margin: '0 auto',
            padding: '0 15px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          {/* 品牌名称 */}
          <a
            href="#page-top"
            style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              color: '#212529',
              textDecoration: 'none',
              marginRight: '1rem'
            }}
          >
            寒链
          </a>

          {/* 导航链接 - 桌面版 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1.5rem'
            }}
          >
            <Link
              href="/"
              style={{
                color: '#212529',
                textDecoration: 'none',
                padding: '0.5rem 0',
                fontSize: '1rem'
              }}
            >
              首页
            </Link>
            <Link
              href="/product"
              style={{
                color: '#212529',
                textDecoration: 'none',
                padding: '0.5rem 0',
                fontSize: '1rem'
              }}
            >
              产品信息
            </Link>
            <Link
              href="/cases"
              style={{
                color: '#212529',
                textDecoration: 'none',
                padding: '0.5rem 0',
                fontSize: '1rem'
              }}
            >
              客户案例
            </Link>
            <Link
              href="/team"
              style={{
                color: '#212529',
                textDecoration: 'none',
                padding: '0.5rem 0',
                fontSize: '1rem'
              }}
            >
              团队
            </Link>
            <Link
              href="/contact"
              style={{
                color: '#212529',
                textDecoration: 'none',
                padding: '0.5rem 0',
                fontSize: '1rem'
              }}
            >
              联系我们
            </Link>
          </div>
        </div>
      </nav>
    );
  }

  // 客户端渲染时使用完整的Navigation组件
  return <OriginalNavigation />;
}
