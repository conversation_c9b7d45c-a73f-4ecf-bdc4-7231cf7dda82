'use client';

import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';

// 简化的产品数据
const productData = [
  {
    id: '1',
    title: '工业冰块',
    category: 'industrial',
    description: '专为工业应用设计的高品质冰块，具有优异的冷却性能和持久的保冷效果。',
    image: '/img/industrial-ice-1.jpg',
    features: ['高纯度制冰工艺', '持久保冷效果', '规格可定制', '快速冷却能力', '环保无污染']
  },
  {
    id: '2',
    title: '食用冰块',
    category: 'food',
    description: '严格按照食品安全标准生产的食用级冰块，纯净卫生，适用于餐饮、饮品等应用。',
    image: '/img/food-ice-1.jpg',
    features: ['食品级安全标准', '纯净水源制作', '无菌生产环境', '快速冷冻技术', '多种规格可选']
  },
  {
    id: '3',
    title: '干冰产品',
    category: 'industrial',
    description: '高品质干冰产品，具有极低温度和升华特性，适用于特殊冷却、保鲜和舞台效果等应用。',
    image: '/img/dry-ice-1.jpg',
    features: ['极低温冷却', '无残留升华', '环保无毒', '冷却效果持久', '多种形态可选']
  },
  {
    id: '4',
    title: '冰雕定制',
    category: 'service',
    description: '专业冰雕艺术定制服务，为各种活动和场合提供精美的冰雕作品。',
    image: '/img/ice-sculpture-1.jpg',
    features: ['专业艺术设计', '精湛雕刻工艺', '个性化定制', '现场制作服务', '完善售后保障']
  },
  {
    id: '5',
    title: '冰块配送',
    category: 'service',
    description: '专业的冰块配送服务，提供及时、可靠的冷链配送，确保冰块在最佳状态下送达。',
    image: '/img/ice-delivery-1.jpg',
    features: ['24小时配送服务', '专业冷链车辆', 'GPS实时跟踪', '温度监控系统', '灵活配送时间']
  }
];

// 产品分类
const categories = [
  { id: 'all', name: '全部产品', description: '我们提供全系列的冰产品和相关服务，满足各种工业和商业需求。' },
  { id: 'industrial', name: '工业冰产品', description: '专业的工业级冰产品，为各种工业应用提供可靠的冷却解决方案。' },
  { id: 'food', name: '食用冰产品', description: '符合食品安全标准的食用级冰产品，为餐饮行业提供安全可靠的冰块。' },
  { id: 'service', name: '冰产品服务', description: '专业的冰产品相关服务，包括定制、配送等全方位服务。' }
];

export function SimpleProductPage() {
  const [activeCategory, setActiveCategory] = useState('all');

  // 根据分类过滤产品
  const getProductsByCategory = (categoryId: string) => {
    if (categoryId === 'all') {
      return productData;
    }
    return productData.filter(product => product.category === categoryId);
  };

  const currentProducts = getProductsByCategory(activeCategory);
  const currentCategory = categories.find(cat => cat.id === activeCategory);

  return (
    <div style={{ padding: '2rem 0' }}>
      <Helmet>
        <title>产品信息中心 | 上海寒链实业有限公司</title>
        <meta name="description" content="上海寒链实业有限公司提供高品质工业冰和食用冰产品，满足各种工业和商业需求。" />
        <meta name="keywords" content="工业冰产品,食用冰产品,冷链服务,上海寒链" />
      </Helmet>

      <div className="container" style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 页面标题 */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{ 
            fontSize: '2.5rem', 
            fontWeight: 'bold', 
            color: '#333',
            marginBottom: '1rem'
          }}>
            产品信息中心
          </h1>
          <div style={{
            width: '60px',
            height: '4px',
            backgroundColor: '#1890ff',
            margin: '0 auto'
          }}></div>
        </div>

        {/* 分类标签 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          marginBottom: '2rem',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              style={{
                padding: '0.75rem 1.5rem',
                border: activeCategory === category.id ? '2px solid #1890ff' : '2px solid #e0e0e0',
                backgroundColor: activeCategory === category.id ? '#1890ff' : 'white',
                color: activeCategory === category.id ? 'white' : '#333',
                borderRadius: '25px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '500',
                transition: 'all 0.3s ease',
                minWidth: '120px'
              }}
              onMouseEnter={(e) => {
                if (activeCategory !== category.id) {
                  e.currentTarget.style.borderColor = '#1890ff';
                  e.currentTarget.style.color = '#1890ff';
                }
              }}
              onMouseLeave={(e) => {
                if (activeCategory !== category.id) {
                  e.currentTarget.style.borderColor = '#e0e0e0';
                  e.currentTarget.style.color = '#333';
                }
              }}
            >
              {category.name}
              <span style={{
                marginLeft: '0.5rem',
                backgroundColor: activeCategory === category.id ? 'rgba(255,255,255,0.3)' : '#f0f0f0',
                color: activeCategory === category.id ? 'white' : '#666',
                padding: '0.2rem 0.5rem',
                borderRadius: '12px',
                fontSize: '0.8rem'
              }}>
                {getProductsByCategory(category.id).length}
              </span>
            </button>
          ))}
        </div>

        {/* 分类描述 */}
        {currentCategory && (
          <div style={{
            textAlign: 'center',
            marginBottom: '2rem',
            padding: '1rem',
            backgroundColor: '#f9f9f9',
            borderRadius: '8px'
          }}>
            <p style={{ 
              fontSize: '1.1rem', 
              color: '#666', 
              margin: 0,
              lineHeight: 1.6
            }}>
              {currentCategory.description}
            </p>
          </div>
        )}

        {/* 产品列表 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '2rem',
          marginTop: '2rem'
        }}>
          {currentProducts.map(product => (
            <div
              key={product.id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
              }}
              onClick={() => {
                // 跳转到产品详情页面
                window.location.href = `/product/${product.id}`;
              }}
            >
              {/* 产品图片 */}
              <div style={{
                height: '200px',
                overflow: 'hidden',
                backgroundColor: '#f5f5f5'
              }}>
                <img 
                  src={product.image} 
                  alt={product.title}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                  onError={(e) => {
                    e.currentTarget.src = '/img/placeholder-product.jpg';
                  }}
                />
              </div>

              {/* 产品信息 */}
              <div style={{ padding: '1.5rem' }}>
                <h3 style={{
                  fontSize: '1.3rem',
                  fontWeight: 'bold',
                  color: '#333',
                  marginBottom: '0.5rem',
                  margin: '0 0 0.5rem 0'
                }}>
                  {product.title}
                </h3>
                
                <p style={{
                  color: '#666',
                  fontSize: '0.95rem',
                  lineHeight: 1.6,
                  marginBottom: '1rem'
                }}>
                  {product.description}
                </p>

                {/* 产品特点 */}
                <div style={{ marginBottom: '1rem' }}>
                  <h4 style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#333',
                    marginBottom: '0.5rem'
                  }}>
                    产品特点
                  </h4>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '0.5rem'
                  }}>
                    {product.features.slice(0, 3).map((feature, index) => (
                      <span
                        key={index}
                        style={{
                          padding: '0.25rem 0.75rem',
                          backgroundColor: '#f0f0f0',
                          borderRadius: '12px',
                          fontSize: '0.8rem',
                          color: '#666'
                        }}
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 查看详情按钮 */}
                <button
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    backgroundColor: '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '1rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'background-color 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#0056b3';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#1890ff';
                  }}
                >
                  查看详情
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* 如果没有产品 */}
        {currentProducts.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '3rem',
            color: '#666'
          }}>
            <p style={{ fontSize: '1.2rem' }}>该分类下暂无产品</p>
          </div>
        )}
      </div>
    </div>
  );
}
