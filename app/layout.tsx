import type { Metadata } from 'next';

// 保持现有的样式导入 - 完全不变
import '@/index.css';
import '@/App.css';

// 导入SSR兼容组件所需的样式
import '@/features/header/Header.css';
import '@/features/gallery/Gallery.css';
import '@/features/navigation/Navigation.css';
import '@/components/display/Carousel/AntCarousel.css';

// 创建客户端包装器来处理现有提供者组件
import { ClientWrapper } from './ClientWrapper';

// 保持现有的安全初始化 - 完全不变
import { SecurityInit } from '@/utils/securityInit';

// 平滑滚动将在ClientWrapper中初始化

export const metadata: Metadata = {
  title: '寒链 - 工业冰战略合作伙伴',
  description: '专业的工业冰制造和供应商，提供高质量的工业冰产品和服务',
  keywords: '工业冰,制冰,冷链,寒链,冰块制造',
  authors: [{ name: '寒链团队' }],
  robots: 'index, follow',
  openGraph: {
    title: '寒链 - 工业冰战略合作伙伴',
    description: '专业的工业冰制造和供应商',
    type: 'website',
    locale: 'zh_CN',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        {/* Bootstrap CSS - 与原项目保持一致 */}
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
          crossOrigin="anonymous"
        />

        {/* Font Awesome - 与原项目保持一致 */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />

        {/* 保持现有的第三方脚本 - 完全不变 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:3986687,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              var _hmt = _hmt || [];
              (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?518ba7c29aae38e16c0b41d072dd21ae";
                var s = document.getElementsByTagName("script")[0]; 
                s.parentNode.insertBefore(hm, s);
              })();
            `,
          }}
        />
      </head>
      <body>
        {/* 使用客户端包装器来处理现有提供者 - 保持功能完全不变 */}
        <ClientWrapper>
          {/* 保持现有的无障碍跳转链接 - 完全不变 */}
          <a href="#main-content" className="skip-nav">跳到主要内容</a>

          {/* 子页面内容 */}
          {children}
        </ClientWrapper>
      </body>
    </html>
  );
}
