import type { Metadata } from 'next';

// 保持现有的样式导入 - 完全不变
import '@/index.css';
import '@/App.css';

// 保持现有的提供者组件 - 完全不变
import { AppProviders } from '@/components/providers/AppProviders';

// 保持现有的安全初始化 - 完全不变
import { SecurityInit } from '@/utils/securityInit';

// 保持现有的平滑滚动初始化 - 完全不变
import SmoothScroll from "smooth-scroll";

// 初始化平滑滚动功能 - 保持与App.tsx完全相同
if (typeof window !== 'undefined') {
  new SmoothScroll('a[href*="#"]', {
    speed: 800,
    speedAsDuration: true,
  });
}

export const metadata: Metadata = {
  title: '寒链 - 工业冰战略合作伙伴',
  description: '专业的工业冰制造和供应商，提供高质量的工业冰产品和服务',
  keywords: '工业冰,制冰,冷链,寒链,冰块制造',
  authors: [{ name: '寒链团队' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: '寒链 - 工业冰战略合作伙伴',
    description: '专业的工业冰制造和供应商',
    type: 'website',
    locale: 'zh_CN',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        {/* 保持现有的第三方脚本 - 完全不变 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:3986687,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              var _hmt = _hmt || [];
              (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?518ba7c29aae38e16c0b41d072dd21ae";
                var s = document.getElementsByTagName("script")[0]; 
                s.parentNode.insertBefore(hm, s);
              })();
            `,
          }}
        />
      </head>
      <body>
        {/* 保持现有的提供者层级结构 - 完全不变 */}
        <AppProviders>
          {/* 保持现有的安全初始化 - 完全不变 */}
          <SecurityInit />
          
          {/* 保持现有的无障碍跳转链接 - 完全不变 */}
          <a href="#main-content" className="skip-nav">跳到主要内容</a>
          
          {/* 子页面内容 */}
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
