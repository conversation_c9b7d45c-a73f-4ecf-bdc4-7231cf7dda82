'use client';

import type { Metadata } from 'next';

// 使用SSR兼容的导航组件
import { SSRCompatibleNavigation } from '../components/SSRCompatibleNavigation';
import { Testimonials } from '@/features/testimonials';
import { PageFooter } from '@/features/contact/PageFooter';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../components/NextScrollToTop';
import { HelmetProvider } from 'react-helmet-async';

// metadata将通过layout.tsx处理

export default function TestimonialsPageRoute() {
  // 保持现有的TestimonialsLayout配置 - 完全不变
  const testimonialsLayoutConfig = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
      recordNumbers: ["沪ICP备**********号", "沪ICP备**********号-1"]
    },
    contactFloatBarConfig: {
      items: defaultContactItems,
      themeColor: "#1890ff",
      top: "50%"
    }
  };

  // 默认的客户评价数据
  const defaultTestimonialsData = {
    description: "我们的客户对我们的服务和产品的评价",
    testimonials: [
      {
        name: "张先生",
        position: "技术总监",
        company: "某科技有限公司",
        text: "产品质量非常好，服务也很到位，我们公司非常满意。",
        img: "/img/testimonials/person1.jpg"
      },
      {
        name: "李女士",
        position: "采购经理",
        company: "某制造企业",
        text: "合作多年，一直保持着良好的合作关系，值得信赖的供应商。",
        img: "/img/testimonials/person2.jpg"
      },
      {
        name: "王总",
        position: "总经理",
        company: "某餐饮连锁",
        text: "冰产品质量稳定，配送及时，是我们长期合作的好伙伴。",
        img: "/img/testimonials/person3.jpg"
      },
      {
        name: "陈经理",
        position: "运营经理",
        company: "某物流公司",
        text: "专业的冷链服务，帮助我们提升了运输效率和产品质量。",
        img: "/img/testimonials/person4.jpg"
      }
    ]
  };

  return (
    <HelmetProvider>
      {/* 保持现有的TestimonialsLayout结构 - 完全不变 */}
      <div className={testimonialsLayoutConfig.containerClassName}>
        <NextScrollToTop />

        <SSRCompatibleNavigation />

        {/* 页面标题 */}
        <div className="page-header bg-light py-5 mt-5">
          <div className="container">
            <div className="row">
              <div className="col-12 text-center">
                <h1 className="display-4 fw-bold">客户评价</h1>
                <p className="lead">听听我们客户的真实声音</p>
              </div>
            </div>
          </div>
        </div>

        <main 
          id="main-content" 
          tabIndex={-1} 
          className={testimonialsLayoutConfig.mainClassName}
        >
          {/* 保持现有的Testimonials组件完全不变 */}
          <Testimonials data={defaultTestimonialsData} />
        </main>

        <PageFooter {...testimonialsLayoutConfig.footerConfig} />

        <ContactFloatBar {...testimonialsLayoutConfig.contactFloatBarConfig} />
      </div>
    </HelmetProvider>
  );
}
