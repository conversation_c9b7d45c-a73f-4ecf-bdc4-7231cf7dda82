'use client';

import { useParams, useRouter } from 'next/navigation';
import { SSRCompatibleNavigation } from '../../components/SSRCompatibleNavigation';
import { PageFooter } from '@/features/contact/PageFooter';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';
import { NextScrollToTop } from '../../components/NextScrollToTop';
import { HelmetProvider } from 'react-helmet-async';

// 案例分类枚举
enum CaseCategory {
  COOLING = 'cooling',
  EDIBLE = 'edible',
  INDUSTRIAL = 'industrial'
}

// 案例数据接口
interface CaseItem {
  id: string;
  category: CaseCategory;
  clientName: string;
  industry: string;
  thumbnail: string;
  scenario: string;
  description?: string;
  images?: string[];
}

// 模拟案例数据 - 与原项目保持一致
const caseData: CaseItem[] = [
  {
    id: '1',
    category: CaseCategory.COOLING,
    clientName: '某高端酒店',
    industry: '酒店行业',
    thumbnail: '/img/cases/hotel.jpg',
    scenario: '中央空调系统降温',
    description: '采用定制冰晶降温系统，为酒店提供高效节能的制冷解决方案。通过精确的温度控制和智能化管理，成功降低了30%的能耗，同时确保了客房的舒适温度。该项目展示了我们在商业制冷领域的专业技术和创新能力。',
    images: ['/img/cases/hotel-1.jpg', '/img/cases/hotel-2.jpg', '/img/cases/hotel-3.jpg']
  },
  {
    id: '2',
    category: CaseCategory.EDIBLE,
    clientName: '某连锁餐厅',
    industry: '餐饮行业',
    thumbnail: '/img/cases/mall.jpg',
    scenario: '食品保鲜与展示',
    description: '为连锁餐厅提供食用级冰块和保鲜解决方案。采用严格的食品安全标准，确保冰块的纯净度和安全性。通过专业的冷链配送服务，保证食材的新鲜度，提升了餐厅的食品质量和顾客满意度。',
    images: ['/img/cases/restaurant-1.jpg', '/img/cases/restaurant-2.jpg']
  },
  {
    id: '3',
    category: CaseCategory.INDUSTRIAL,
    clientName: '某化工厂',
    industry: '化工行业',
    thumbnail: '/img/placeholder-case.jpg',
    scenario: '生产设备冷却',
    description: '为化工厂提供工业级冰块用于高温设备降温。通过定制化的冷却方案，有效控制生产设备的运行温度，提高了生产效率和设备使用寿命。该项目体现了我们在工业制冷领域的专业实力。',
    images: ['/img/cases/factory-1.jpg', '/img/cases/factory-2.jpg', '/img/cases/factory-3.jpg']
  },
  {
    id: '4',
    category: CaseCategory.COOLING,
    clientName: '某大型商场',
    industry: '商业地产',
    thumbnail: '/img/placeholder-case.jpg',
    scenario: '商场中央空调辅助降温',
    description: '为大型商场提供中央空调辅助降温服务，在高峰期有效缓解空调系统压力，确保商场内部温度的舒适性。',
    images: ['/img/cases/mall-1.jpg', '/img/cases/mall-2.jpg']
  },
  {
    id: '5',
    category: CaseCategory.EDIBLE,
    clientName: '某海鲜市场',
    industry: '食品零售',
    thumbnail: '/img/placeholder-case.jpg',
    scenario: '海鲜保鲜',
    description: '为海鲜市场提供专业的保鲜冰块，确保海鲜产品的新鲜度和品质，延长保存时间。',
    images: ['/img/cases/seafood-1.jpg']
  }
];

export default function CaseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params?.id as string;
  
  // 查找对应的案例
  const caseItem = caseData.find(item => item.id === id);

  // 返回按钮处理
  const handleBack = () => {
    router.push('/cases');
  };

  // 如果案例不存在
  if (!caseItem) {
    return (
      <HelmetProvider>
        <div className="d-flex flex-column min-vh-100">
          <NextScrollToTop />
          <SSRCompatibleNavigation />
          
          <main id="main-content" tabIndex={-1} className="flex-grow-1 mt-5 pt-4">
            <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
              <div style={{ 
                textAlign: 'center', 
                padding: '4rem 2rem',
                color: '#666',
                fontSize: '1.2rem'
              }}>
                <h1 style={{ color: '#333', marginBottom: '1rem' }}>案例不存在</h1>
                <p>抱歉，您访问的案例页面不存在。</p>
                <button
                  onClick={handleBack}
                  style={{
                    marginTop: '2rem',
                    padding: '0.75rem 2rem',
                    backgroundColor: '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '1rem'
                  }}
                >
                  返回案例列表
                </button>
              </div>
            </div>
          </main>

          <PageFooter recordNumbers={["沪ICP备**********号", "沪ICP备**********号-1"]} />
          <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
        </div>
      </HelmetProvider>
    );
  }

  return (
    <HelmetProvider>
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        
        <main id="main-content" tabIndex={-1} className="flex-grow-1 mt-5 pt-4">
          <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
            {/* 返回按钮 */}
            <button
              onClick={handleBack}
              style={{
                marginBottom: '2rem',
                padding: '0.5rem 1rem',
                backgroundColor: '#f0f0f0',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.9rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              ← 返回案例列表
            </button>

            {/* 页面标题 */}
            <h1 style={{ 
              textAlign: 'center', 
              marginBottom: '2rem', 
              color: '#333', 
              fontSize: '2rem' 
            }}>
              {caseItem.clientName}案例详情
            </h1>

            {/* 案例内容 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '2rem' }}>
              {/* 主图片 */}
              <div style={{
                width: '100%',
                height: '400px',
                overflow: 'hidden',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
              }}>
                <img 
                  src={caseItem.thumbnail} 
                  alt={caseItem.clientName}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                  onError={(e) => {
                    e.currentTarget.src = '/img/placeholder-case.jpg';
                  }}
                />
              </div>

              {/* 详细信息 */}
              <div style={{
                background: 'white',
                padding: '2rem',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
              }}>
                <h2 style={{ 
                  marginTop: 0, 
                  color: '#333', 
                  fontSize: '1.5rem',
                  marginBottom: '1rem'
                }}>
                  {caseItem.clientName}
                </h2>
                <p style={{ margin: '1rem 0', color: '#555', lineHeight: 1.6 }}>
                  <strong style={{ color: '#333' }}>行业:</strong> {caseItem.industry}
                </p>
                <p style={{ margin: '1rem 0', color: '#555', lineHeight: 1.6 }}>
                  <strong style={{ color: '#333' }}>应用场景:</strong> {caseItem.scenario}
                </p>
                {caseItem.description && (
                  <div style={{
                    padding: '1rem',
                    background: '#f9f9f9',
                    borderRadius: '4px',
                    fontSize: '0.95rem',
                    color: '#555',
                    lineHeight: 1.6,
                    marginTop: '1rem'
                  }}>
                    {caseItem.description}
                  </div>
                )}
              </div>

              {/* 案例图片画廊 */}
              {caseItem.images && caseItem.images.length > 0 && (
                <div style={{ marginTop: '2rem' }}>
                  <h3 style={{ 
                    marginBottom: '1rem', 
                    color: '#333', 
                    fontSize: '1.3rem' 
                  }}>
                    案例图片
                  </h3>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                    gap: '1rem'
                  }}>
                    {caseItem.images.map((image, index) => (
                      <div
                        key={index}
                        style={{
                          height: '200px',
                          overflow: 'hidden',
                          borderRadius: '8px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-3px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                        }}
                      >
                        <img 
                          src={image} 
                          alt={`案例图片 ${index + 1}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                          onError={(e) => {
                            e.currentTarget.src = '/img/placeholder-case.jpg';
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>

        <PageFooter recordNumbers={["沪ICP备**********号", "沪ICP备**********号-1"]} />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </HelmetProvider>
  );
}
