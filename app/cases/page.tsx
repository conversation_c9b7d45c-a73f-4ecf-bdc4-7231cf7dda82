'use client';

import React, { useState } from 'react';
import { NextScrollToTop } from '../components/NextScrollToTop';
import { SSRCompatibleNavigation } from '../components/SSRCompatibleNavigation';
import { Contact } from '@/features/contact';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { CookieConsent } from '@/features/cookie-consent';
import JsonData from '@/data/data.json';

// 导入默认联系方式配置
import { defaultContactItems } from '@/features/contact-float-bar/data/contactItems';

// 案例分类枚举
enum CaseCategory {
  COOLING = 'cooling',
  EDIBLE = 'edible',
  INDUSTRIAL = 'industrial'
}

// 案例数据接口
export interface CaseItem {
  id: string;
  category: CaseCategory;
  clientName: string;
  industry: string;
  thumbnail: string;
  scenario: string;
  description?: string;
  images?: string[];
}

// 模拟案例数据
export const caseData: CaseItem[] = [
  {
    id: '1',
    category: CaseCategory.COOLING,
    clientName: '某高端酒店',
    industry: '酒店行业',
    thumbnail: '/img/cases/hotel.jpg',
    scenario: '中央空调系统降温',
    description: '采用定制冰晶降温系统，节能30%',
    images: ['/img/cases/hotel-1.jpg', '/img/cases/hotel-2.jpg']
  },
  {
    id: '2',
    category: CaseCategory.EDIBLE,
    clientName: '某连锁餐厅',
    industry: '餐饮行业',
    thumbnail: '/img/cases/restaurant.jpg',
    scenario: '食品保鲜与展示',
    description: '使用食用级冰块，保证食品安全',
    images: ['/img/cases/restaurant-1.jpg']
  },
  {
    id: '3',
    category: CaseCategory.INDUSTRIAL,
    clientName: '某化工厂',
    industry: '化工行业',
    thumbnail: '/img/cases/factory.jpg',
    scenario: '生产设备冷却',
    description: '工业级冰块用于高温设备降温',
    images: ['/img/cases/factory-1.jpg', '/img/cases/factory-2.jpg']
  },
  {
    id: '4',
    category: CaseCategory.COOLING,
    clientName: '某购物中心',
    industry: '商业地产',
    thumbnail: '/img/cases/mall.jpg',
    scenario: '大型商场降温',
    description: '环保节能的冰晶降温解决方案',
    images: ['/img/cases/mall-1.jpg']
  },
  {
    id: '5',
    category: CaseCategory.EDIBLE,
    clientName: '某海鲜市场',
    industry: '食品零售',
    thumbnail: '/img/cases/seafood.jpg',
    scenario: '海鲜保鲜展示',
    description: '专业食用冰，保持海鲜新鲜度',
    images: ['/img/cases/seafood-1.jpg', '/img/cases/seafood-2.jpg']
  },
  {
    id: '6',
    category: CaseCategory.INDUSTRIAL,
    clientName: '某钢铁厂',
    industry: '重工业',
    thumbnail: '/img/cases/steel.jpg',
    scenario: '高温设备冷却',
    description: '工业级冰块，有效降低设备温度',
    images: ['/img/cases/steel-1.jpg']
  }
];

export default function CasesPage() {
  const [activeCategory, setActiveCategory] = useState<CaseCategory>(CaseCategory.COOLING);
  const data = JsonData;

  // 过滤当前分类的案例
  const filteredCases = caseData.filter(caseItem => caseItem.category === activeCategory);

  // 处理案例点击
  const handleCaseClick = (caseId: string) => {
    // 在Next.js中使用window.location进行跳转
    if (typeof window !== 'undefined') {
      window.location.href = `/cases/${caseId}`;
    }
  };

  return (
    <>
      <div className="d-flex flex-column min-vh-100">
        <NextScrollToTop />
        <SSRCompatibleNavigation />
        <main id="main-content" tabIndex={-1} className="flex-grow-1">
          <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
            <h1 style={{ 
              textAlign: 'center', 
              marginBottom: '2rem', 
              color: '#333', 
              fontSize: '2rem' 
            }}>
              客户案例展示
            </h1>

            {/* 分类切换标签 */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              marginBottom: '2rem', 
              gap: '1rem', 
              flexWrap: 'wrap' 
            }}>
              {Object.values(CaseCategory).map(category => (
                <button
                  key={category}
                  style={{
                    padding: '0.75rem 1.5rem',
                    border: 'none',
                    borderRadius: '4px',
                    background: activeCategory === category ? '#1890ff' : '#f0f0f0',
                    color: activeCategory === category ? 'white' : '#333',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '1rem',
                    fontWeight: '500'
                  }}
                  onClick={() => setActiveCategory(category)}
                  onMouseEnter={(e) => {
                    if (activeCategory !== category) {
                      e.currentTarget.style.background = '#e0e0e0';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (activeCategory !== category) {
                      e.currentTarget.style.background = '#f0f0f0';
                    }
                  }}
                >
                  {category === CaseCategory.COOLING && '降温冰'}
                  {category === CaseCategory.EDIBLE && '食用冰'}
                  {category === CaseCategory.INDUSTRIAL && '工业冰'}
                </button>
              ))}
            </div>

            {/* 案例列表 */}
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', 
              gap: '2rem', 
              marginTop: '1rem' 
            }}>
              {filteredCases.map(caseItem => (
                <div
                  key={caseItem.id}
                  style={{
                    borderRadius: '8px',
                    overflow: 'hidden',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    background: 'white'
                  }}
                  onClick={() => handleCaseClick(caseItem.id)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-5px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                  }}
                >
                  <div style={{ height: '200px', overflow: 'hidden' }}>
                    <img 
                      src={caseItem.thumbnail} 
                      alt={caseItem.clientName}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        transition: 'transform 0.3s ease'
                      }}
                      onError={(e) => {
                        // 图片加载失败时使用占位图
                        e.currentTarget.src = '/img/placeholder-case.jpg';
                      }}
                    />
                  </div>
                  <div style={{ padding: '1.5rem' }}>
                    <h3 style={{ 
                      margin: '0 0 0.5rem', 
                      color: '#333', 
                      fontSize: '1.2rem' 
                    }}>
                      {caseItem.clientName}
                    </h3>
                    <p style={{ 
                      margin: '0 0 0.5rem', 
                      color: '#666', 
                      fontSize: '0.9rem' 
                    }}>
                      {caseItem.industry}
                    </p>
                    <p style={{ 
                      margin: '0', 
                      color: '#888', 
                      fontSize: '0.8rem' 
                    }}>
                      应用场景: {caseItem.scenario}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* 如果没有案例数据 */}
            {filteredCases.length === 0 && (
              <div style={{ 
                textAlign: 'center', 
                padding: '2rem', 
                color: '#666', 
                fontSize: '1.1rem' 
              }}>
                暂无该分类的案例数据
              </div>
            )}
          </div>
        </main>
        <Contact data={data.Contact} />
        <CookieConsent />
        <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
      </div>
    </>
  );
}
