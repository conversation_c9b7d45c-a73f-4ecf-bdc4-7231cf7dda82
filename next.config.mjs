/** @type {import('next').NextConfig} */
const nextConfig = {
  // 保持现有的开发体验
  reactStrictMode: true,
  swcMinify: true,
  
  // 保持现有的路径别名
  experimental: {
    typedRoutes: false, // 暂时禁用，保持现有路由方式
  },
  
  // 保持现有的静态资源处理
  images: {
    unoptimized: true, // 保持现有图片处理方式，不使用Next.js优化
  },
  
  // 保持现有的构建输出
  output: 'export', // 静态导出，兼容现有部署方式
  distDir: 'dist', // 保持现有输出目录
  trailingSlash: true, // 保持现有URL格式
  
  // 保持现有的webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 保持现有的别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, './src'),
      '#': require('path').resolve(__dirname, './src/types'),
    };
    
    // 保持现有的文件处理
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    
    // 保持现有的分包策略
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'vendor',
            chunks: 'all',
          },
          antd: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: 'antd',
            chunks: 'all',
          },
          router: {
            test: /[\\/]node_modules[\\/]react-router-dom[\\/]/,
            name: 'router',
            chunks: 'all',
          },
        },
      };
    }
    
    return config;
  },
  
  // 保持现有的重写规则
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'https://api.starrier.org/:path*',
      },
    ];
  },
  
  // 保持现有的安全头配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://hm.baidu.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self' https://api.starrier.org https://www.google-analytics.com https://analytics.google.com https://www.googletagmanager.com https://hm.baidu.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self';"
          },
        ],
      },
    ];
  },
};

export default nextConfig;
