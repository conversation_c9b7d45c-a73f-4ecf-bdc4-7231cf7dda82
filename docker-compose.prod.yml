# 生产环境覆盖配置
version: '3.8'

services:
  frontend:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    environment:
      NODE_ENV: production
      VITE_USE_MOCK: false
      VITE_ENABLE_DEBUG: false
      VITE_ENABLE_ANALYTICS: true
      VITE_ENABLE_ERROR_REPORTING: true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
  # 生产环境启用代理服务
  nginx-proxy:
    profiles: []  # 移除 profiles 使其默认启动
    volumes:
      - nginx-cache:/var/cache/nginx
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
