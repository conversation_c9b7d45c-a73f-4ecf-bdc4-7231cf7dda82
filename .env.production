# 自动生成的 production 环境配置
# 生成时间: 2025-06-04T06:16:12.485Z

VITE_API_BASE_URL=/api
VITE_APP_BASE_PATH=/
VITE_USE_MOCK=false
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false
VITE_REQUEST_TIMEOUT=10000
VITE_MAX_RETRY_ATTEMPTS=3
VITE_SESSION_TIMEOUT=30
VITE_CDN_BASE_URL=https://cdn.jsdelivr.net
VITE_FONT_CDN_URL=https://fonts.googleapis.com
VITE_EMAILJS_SERVICE_ID=
VITE_EMAILJS_TEMPLATE_ID=
VITE_EMAILJS_PUBLIC_KEY=
VITE_ANALYTICS_API=https://api.starrier.org/analytics
VITE_ANALYTICS_TRACKING_ID=
NODE_ENV=production
VITE_PROD_DOMAIN=frologi.com
VITE_ENABLE_HTTPS=true
VITE_ENABLE_GZIP=true

# 注意：此文件由 config/env.config.cjs 自动生成
# 请勿手动编辑，修改请在 env.config.cjs 中进行