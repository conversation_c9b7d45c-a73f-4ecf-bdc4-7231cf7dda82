# 使用 Node.js 镜像 - 指定具体版本以提高安全性
FROM node:20.11.1-alpine AS builder

# 安装安全更新
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 设置工作目录
WORKDIR /app

# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖，包括开发依赖
RUN yarn install --network-timeout 300000 --frozen-lockfile --production=false

# 确保安装了必要的开发依赖
RUN yarn add @vitejs/plugin-react path --dev

# 复制源代码
COPY . .

# 更改文件所有者
RUN chown -R nextjs:nodejs /app

# 切换到非root用户
USER nextjs

# 显示环境信息
RUN echo "Node version: $(node -v)" && \
    echo "Yarn version: $(yarn -v)" && \
    echo "Environment: production"

# 直接使用 Vite CLI 构建，不依赖配置文件
RUN yarn vite build --outDir=dist --emptyOutDir --minify=terser

# 第二阶段：使用 Nginx 镜像 - 指定具体版本
FROM nginx:1.25.3-alpine

# 安装安全更新
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# 创建非root用户用于运行nginx
RUN addgroup -g 1001 -S nginx-user && \
    adduser -S nginx-user -u 1001 -G nginx-user

# 删除默认的 Nginx 配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制自定义 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/

# 创建环境变量注入脚本
RUN echo '#!/bin/sh' > /docker-entrypoint.d/40-inject-env.sh && \
    echo 'envsubst < /usr/share/nginx/html/env-config.template.js > /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-inject-env.sh && \
    chmod +x /docker-entrypoint.d/40-inject-env.sh

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 设置正确的文件权限
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html && \
    chown -R nginx-user:nginx-user /var/cache/nginx && \
    chown -R nginx-user:nginx-user /var/log/nginx && \
    chown -R nginx-user:nginx-user /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx-user:nginx-user /var/run/nginx.pid

# 切换到非root用户
USER nginx-user

# 暴露端口
EXPOSE 8080

# 使用dumb-init启动nginx以正确处理信号
ENTRYPOINT ["dumb-init", "--"]
CMD ["nginx", "-g", "daemon off;"]
