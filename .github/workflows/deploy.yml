name: Deploy <PERSON><PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - tec-analyse-data-rext-reactor      # 提交代码到 main 分支时触发部署
    paths-ignore: # 规定以下文件变更不触发部署
      - README.md
      - LICENSE

jobs:
  deploy:
    runs-on: ubuntu-latest # 使用ubuntu系统镜像
    steps:
      - name: Get version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v2.2.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push Docker image
        id: docker_build
        run: |
          # 给构建脚本添加执行权限
          chmod +x ./build.sh
          # 执行构建脚本，并推送镜像
          ./build.sh --push

      - name: Images info
        run: |
          echo "Image built and pushed: starrier/frostchain:0.0.1"
          docker images starrier/frostchain:0.0.1 --format "{{.ID}} {{.Size}}"
