{
  "compilerOptions": {
    // 保持现有的TypeScript配置
    "target": "ESNext",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "useDefineForClassFields": true,
    "skipLibCheck": true,
    
    // Next.js特定配置
    "allowJs": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    
    // 保持现有的严格检查
    "strictNullChecks": true,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "useUnknownInCatchVariables": false,
    
    // 保持现有的路径映射
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "#/*": ["./src/types/*"]
    },
    
    // 保持现有的类型根目录
    "typeRoots": ["node_modules/@types", "src/types"],
    "types": ["node"],
    
    // Next.js插件
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "src",
    "src/types",
    "tailwind.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
