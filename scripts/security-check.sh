#!/bin/bash

# 安全检查脚本
# 用于检查项目的安全配置和潜在风险

set -e

echo "🔒 开始安全检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local command="$2"
    local severity="$3" # error, warning, info
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查: $description ... "
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 通过${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        case $severity in
            "error")
                echo -e "${RED}✗ 失败${NC}"
                FAILED_CHECKS=$((FAILED_CHECKS + 1))
                ;;
            "warning")
                echo -e "${YELLOW}⚠ 警告${NC}"
                WARNING_CHECKS=$((WARNING_CHECKS + 1))
                ;;
            *)
                echo -e "${BLUE}ℹ 信息${NC}"
                ;;
        esac
        return 1
    fi
}

# 1. 检查依赖安全性
echo -e "\n${BLUE}=== 依赖安全检查 ===${NC}"

check_item "检查是否存在已知漏洞的依赖" "npm audit --audit-level=moderate" "warning"

check_item "检查package.json中的依赖版本" "grep -E '\"[^\"]*\": \"[~^]' package.json" "warning"

# 2. 检查敏感信息泄露
echo -e "\n${BLUE}=== 敏感信息检查 ===${NC}"

check_item "检查是否有硬编码的API密钥" "! grep -r -i 'api[_-]key\|secret\|password' src/ --include='*.ts' --include='*.tsx' --include='*.js' --include='*.jsx'" "error"

check_item "检查是否有硬编码的令牌" "! grep -r -E '(token|jwt|bearer).*[=:]\s*[\"'\'']\w{20,}' src/ --include='*.ts' --include='*.tsx'" "error"

check_item "检查是否有调试信息泄露" "! grep -r 'console\.log\|debugger' src/ --include='*.ts' --include='*.tsx'" "warning"

check_item "检查是否有危险的DOM操作" "! grep -r 'dangerouslySetInnerHTML\|innerHTML\|document\.write' src/ --include='*.ts' --include='*.tsx'" "error"

check_item "检查是否有不安全的eval使用" "! grep -r 'eval\|Function.*string\|setTimeout.*string\|setInterval.*string' src/ --include='*.ts' --include='*.tsx'" "error"

# 3. 检查配置文件安全性
echo -e "\n${BLUE}=== 配置文件安全检查 ===${NC}"

check_item "检查.env文件是否在.gitignore中" "grep -q '\.env' .gitignore" "error"

check_item "检查是否存在.env.example文件" "test -f .env.example" "warning"

check_item "检查Dockerfile是否使用非root用户" "grep -q 'USER' Dockerfile" "error"

check_item "检查nginx配置是否包含安全头" "grep -q 'X-Content-Type-Options' nginx.conf" "error"

# 4. 检查代码质量和安全实践
echo -e "\n${BLUE}=== 代码安全实践检查 ===${NC}"

check_item "检查是否使用了TypeScript" "test -f tsconfig.json" "warning"

check_item "检查是否配置了ESLint" "test -f eslint.config.js || test -f .eslintrc.js || test -f .eslintrc.json" "warning"

check_item "检查是否有输入验证" "grep -r 'validate\|sanitize' src/ --include='*.ts' --include='*.tsx'" "warning"

# 5. 检查构建和部署安全性
echo -e "\n${BLUE}=== 构建部署安全检查 ===${NC}"

check_item "检查是否使用了具体的Docker镜像版本" "! grep -E 'FROM.*:latest' Dockerfile" "warning"

check_item "检查是否配置了健康检查" "grep -q 'HEALTHCHECK' Dockerfile || grep -q 'healthcheck' docker-compose.yml" "warning"

check_item "检查构建脚本是否安全" "test -f build.sh && ! grep -E 'rm -rf /|sudo|su ' build.sh" "error"

# 6. 检查网络安全配置
echo -e "\n${BLUE}=== 网络安全检查 ===${NC}"

check_item "检查是否配置了CORS" "grep -r 'cors\|Cross-Origin' src/ --include='*.ts' --include='*.tsx'" "warning"

check_item "检查是否使用HTTPS" "grep -q 'https' nginx.conf || grep -q 'ssl' nginx.conf" "warning"

# 7. 检查文件权限
echo -e "\n${BLUE}=== 文件权限检查 ===${NC}"

if [ -f "build.sh" ]; then
    check_item "检查构建脚本是否可执行" "test -x build.sh" "warning"
fi

check_item "检查敏感文件权限" "! find . -name '*.key' -o -name '*.pem' -o -name '*.p12' | xargs ls -la 2>/dev/null | grep -E '^-.{6}r.{2}'" "error"

# 8. 检查第三方服务配置
echo -e "\n${BLUE}=== 第三方服务安全检查 ===${NC}"

check_item "检查EmailJS配置是否安全" "! grep -r 'YOUR_SERVICE_ID\|YOUR_TEMPLATE_ID\|YOUR_PUBLIC_KEY' src/" "error"

check_item "检查是否有未使用的第三方库" "command -v depcheck && depcheck --ignores='@types/*,eslint*'" "warning"

# 9. 检查运行时安全
echo -e "\n${BLUE}=== 运行时安全检查 ===${NC}"

check_item "检查是否配置了错误边界" "grep -r 'ErrorBoundary' src/ --include='*.tsx'" "warning"

check_item "检查是否有内存泄漏防护" "grep -r 'useEffect.*return\|cleanup\|abort' src/ --include='*.ts' --include='*.tsx'" "info"

# 10. 检查劫持防护
echo -e "\n${BLUE}=== 劫持防护检查 ===${NC}"

check_item "检查是否配置了X-Frame-Options" "grep -q 'X-Frame-Options' nginx.conf" "error"

check_item "检查是否禁用了iframe嵌入" "grep -q 'frame-src.*none' nginx.conf" "error"

check_item "检查是否有劫持检测代码" "grep -r 'HijackingDetector\|hijackingProtection' src/ --include='*.ts' --include='*.tsx'" "warning"

check_item "检查是否有安全导航" "grep -r 'SecureNavigation' src/ --include='*.ts' --include='*.tsx'" "warning"

check_item "检查是否有跨域安全头" "grep -q 'Cross-Origin' nginx.conf" "warning"

# 11. 生成安全报告
echo -e "\n${BLUE}=== 生成安全报告 ===${NC}"

SECURITY_REPORT="security-report-$(date +%Y%m%d-%H%M%S).txt"

cat > "$SECURITY_REPORT" << EOF
# 安全检查报告
生成时间: $(date)
项目路径: $(pwd)

## 检查统计
- 总检查项: $TOTAL_CHECKS
- 通过: $PASSED_CHECKS
- 失败: $FAILED_CHECKS  
- 警告: $WARNING_CHECKS

## 安全建议

### 高优先级修复项
1. 移除硬编码的敏感信息
2. 配置适当的文件权限
3. 使用非root用户运行容器
4. 配置完整的安全头

### 中优先级改进项
1. 启用依赖安全扫描
2. 配置HTTPS
3. 实施输入验证
4. 添加错误处理

### 低优先级优化项
1. 移除调试代码
2. 优化构建配置
3. 添加健康检查
4. 配置监控

## 推荐的安全工具
- npm audit: 依赖漏洞扫描
- ESLint Security Plugin: 代码安全检查
- Snyk: 综合安全扫描
- OWASP ZAP: Web应用安全测试

EOF

echo "安全报告已生成: $SECURITY_REPORT"

# 显示总结
echo -e "\n${BLUE}=== 安全检查总结 ===${NC}"
echo -e "总检查项: $TOTAL_CHECKS"
echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
echo -e "${RED}失败: $FAILED_CHECKS${NC}"
echo -e "${YELLOW}警告: $WARNING_CHECKS${NC}"

# 计算安全分数
SECURITY_SCORE=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
echo -e "\n安全分数: $SECURITY_SCORE/100"

if [ $SECURITY_SCORE -ge 80 ]; then
    echo -e "${GREEN}✓ 安全状况良好${NC}"
    exit 0
elif [ $SECURITY_SCORE -ge 60 ]; then
    echo -e "${YELLOW}⚠ 安全状况一般，建议改进${NC}"
    exit 1
else
    echo -e "${RED}✗ 安全风险较高，需要立即修复${NC}"
    exit 2
fi
