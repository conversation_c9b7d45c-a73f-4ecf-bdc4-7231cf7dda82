#!/bin/bash

# 配置管理脚本
# 用于生成和管理项目的各种配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
配置管理脚本

用法: $0 [命令] [选项]

命令:
  generate-env [环境]     生成环境变量文件
  validate-config [环境]  验证配置
  clean-configs          清理旧配置文件
  backup-configs         备份当前配置
  restore-configs        恢复配置备份
  list-configs           列出所有配置文件
  help                   显示此帮助信息

环境选项:
  development, dev       开发环境
  test                   测试环境
  production, prod       生产环境
  docker                 Docker环境
  local                  本地环境

示例:
  $0 generate-env development
  $0 validate-config production
  $0 clean-configs
  $0 backup-configs

EOF
}

# 生成环境变量文件
generate_env() {
    local env=${1:-development}
    
    log_info "生成 $env 环境配置文件..."
    
    # 检查配置生成器是否存在
    if [ ! -f "config/env.config.cjs" ]; then
        log_error "配置生成器不存在: config/env.config.cjs"
        exit 1
    fi

    # 生成配置文件
    node config/env.config.cjs "$env" ".env.$env"
    
    # 如果是默认环境，也生成 .env 文件
    if [ "$env" == "development" ] || [ "$env" == "dev" ]; then
        cp ".env.$env" ".env"
        log_success "已生成默认 .env 文件"
    fi
    
    log_success "环境配置文件生成完成"
}

# 验证配置
validate_config() {
    local env=${1:-development}
    
    log_info "验证 $env 环境配置..."
    
    # 检查环境文件是否存在
    if [ ! -f ".env.$env" ]; then
        log_warning "环境文件不存在: .env.$env"
        log_info "尝试生成配置文件..."
        generate_env "$env"
    fi
    
    # 使用Node.js验证配置
    node -e "
        const { getEnvConfig, validateConfig } = require('./config/env.config.cjs');
        const config = getEnvConfig('$env');
        const validation = validateConfig(config);
        
        console.log('配置验证结果:');
        console.log('- 有效:', validation.isValid ? '✅' : '❌');
        
        if (validation.errors.length > 0) {
            console.log('- 错误:');
            validation.errors.forEach(error => console.log('  ❌', error));
        }
        
        if (validation.warnings.length > 0) {
            console.log('- 警告:');
            validation.warnings.forEach(warning => console.log('  ⚠️ ', warning));
        }
        
        if (!validation.isValid) {
            process.exit(1);
        }
    "
    
    log_success "配置验证完成"
}

# 清理旧配置文件
clean_configs() {
    log_info "清理旧配置文件..."
    
    # 备份重要文件
    backup_configs
    
    # 删除旧的环境文件（保留 .env.example）
    local old_env_files=(
        ".env.development"
        ".env.test" 
        ".env.production"
        ".env.local"
        ".env.docker"
    )
    
    for file in "${old_env_files[@]}"; do
        if [ -f "$file" ]; then
            rm "$file"
            log_success "已删除: $file"
        fi
    done
    
    # 删除旧的Docker配置文件
    local old_docker_files=(
        "docker-compose.local.yml"
    )
    
    for file in "${old_docker_files[@]}"; do
        if [ -f "$file" ]; then
            rm "$file"
            log_success "已删除: $file"
        fi
    done
    
    # 删除旧的构建脚本
    if [ -f "build.sh" ]; then
        rm "build.sh"
        log_success "已删除: build.sh"
    fi
    
    log_success "配置文件清理完成"
}

# 备份配置
backup_configs() {
    local backup_dir="config-backup-$(date +%Y%m%d-%H%M%S)"
    
    log_info "备份配置文件到: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    # 备份所有配置文件
    local config_files=(
        ".env*"
        "docker-compose*.yml"
        "*.config.*"
        "tsconfig*.json"
        "package.json"
        "build.sh"
    )
    
    for pattern in "${config_files[@]}"; do
        for file in $pattern; do
            if [ -f "$file" ]; then
                cp "$file" "$backup_dir/"
                log_info "已备份: $file"
            fi
        done
    done
    
    log_success "配置备份完成: $backup_dir"
}

# 恢复配置
restore_configs() {
    log_info "查找配置备份..."
    
    # 查找最新的备份目录
    local latest_backup=$(ls -1d config-backup-* 2>/dev/null | sort -r | head -1)
    
    if [ -z "$latest_backup" ]; then
        log_error "未找到配置备份"
        exit 1
    fi
    
    log_info "从备份恢复配置: $latest_backup"
    
    # 恢复配置文件
    for file in "$latest_backup"/*; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            cp "$file" "./$filename"
            log_info "已恢复: $filename"
        fi
    done
    
    log_success "配置恢复完成"
}

# 列出配置文件
list_configs() {
    log_info "当前配置文件列表:"
    
    echo ""
    echo "📄 环境配置文件:"
    ls -la .env* 2>/dev/null || echo "  无环境配置文件"
    
    echo ""
    echo "🐳 Docker配置文件:"
    ls -la docker-compose*.yml Dockerfile 2>/dev/null || echo "  无Docker配置文件"
    
    echo ""
    echo "⚙️  构建配置文件:"
    ls -la *.config.* tsconfig*.json 2>/dev/null || echo "  无构建配置文件"
    
    echo ""
    echo "📦 包管理文件:"
    ls -la package*.json yarn.lock 2>/dev/null || echo "  无包管理文件"
    
    echo ""
    echo "🛠️  脚本文件:"
    ls -la scripts/*.sh build.sh 2>/dev/null || echo "  无脚本文件"
}

# 初始化新的配置结构
init_configs() {
    log_info "初始化新的配置结构..."
    
    # 创建必要的目录
    mkdir -p config scripts
    
    # 生成所有环境的配置文件
    local environments=("development" "test" "production" "docker" "local")
    
    for env in "${environments[@]}"; do
        generate_env "$env"
    done
    
    # 设置脚本权限
    chmod +x scripts/*.sh 2>/dev/null || true
    
    log_success "配置结构初始化完成"
}

# 主函数
main() {
    case "${1:-help}" in
        "generate-env"|"gen-env")
            generate_env "$2"
            ;;
        "validate-config"|"validate")
            validate_config "$2"
            ;;
        "clean-configs"|"clean")
            clean_configs
            ;;
        "backup-configs"|"backup")
            backup_configs
            ;;
        "restore-configs"|"restore")
            restore_configs
            ;;
        "list-configs"|"list")
            list_configs
            ;;
        "init-configs"|"init")
            init_configs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
