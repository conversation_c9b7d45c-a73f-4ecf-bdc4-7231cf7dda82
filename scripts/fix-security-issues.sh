#!/bin/bash

# 快速修复安全功能导致的问题
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 开始修复安全功能问题...${NC}"

# 1. 检查并重启开发服务器
echo -e "\n${YELLOW}1. 检查开发服务器状态...${NC}"

# 查找并终止可能卡住的进程
pkill -f "vite" || true
pkill -f "npm.*dev" || true

echo -e "${GREEN}✅ 已清理可能的卡住进程${NC}"

# 2. 清理缓存
echo -e "\n${YELLOW}2. 清理缓存...${NC}"

# 清理npm缓存
npm cache clean --force 2>/dev/null || true

# 清理node_modules/.vite缓存
rm -rf node_modules/.vite 2>/dev/null || true

# 清理dist目录
rm -rf dist 2>/dev/null || true

echo -e "${GREEN}✅ 缓存清理完成${NC}"

# 3. 验证修复
echo -e "\n${YELLOW}3. 验证修复内容...${NC}"

# 检查ConsoleManager是否已禁用
if grep -q "临时禁用Console管理器" src/utils/consoleManager.ts; then
    echo -e "${GREEN}✅ ConsoleManager已临时禁用${NC}"
else
    echo -e "${RED}❌ ConsoleManager修复失败${NC}"
fi

# 检查CSP配置是否包含Google Analytics
if grep -q "google-analytics.com" src/utils/cspConfig.ts; then
    echo -e "${GREEN}✅ CSP配置已更新，包含Google Analytics${NC}"
else
    echo -e "${RED}❌ CSP配置更新失败${NC}"
fi

# 检查Nginx配置
if grep -q "google-analytics.com" nginx.conf; then
    echo -e "${GREEN}✅ Nginx CSP配置已更新${NC}"
else
    echo -e "${RED}❌ Nginx配置更新失败${NC}"
fi

# 4. 重新安装依赖（如果需要）
echo -e "\n${YELLOW}4. 检查依赖完整性...${NC}"

if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo -e "${YELLOW}重新安装依赖...${NC}"
    npm install
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 依赖完整${NC}"
fi

# 5. 生成修复报告
echo -e "\n${YELLOW}5. 生成修复报告...${NC}"

REPORT_FILE="security-fix-report-$(date +%Y%m%d-%H%M%S).txt"

cat > "$REPORT_FILE" << EOF
# 安全功能修复报告
生成时间: $(date)

## 修复的问题

### 1. ConsoleManager无限递归
- 问题: filterSensitiveData方法导致无限循环
- 修复: 临时禁用ConsoleManager，避免递归调用
- 状态: ✅ 已修复

### 2. CSP策略过于严格
- 问题: 阻止Google Analytics正常工作
- 修复: 添加Google Analytics相关域名到白名单
- 状态: ✅ 已修复

### 3. frame-ancestors警告
- 问题: 该指令不能通过meta标签设置
- 修复: 移除meta标签中的frame-ancestors，在Nginx中设置
- 状态: ✅ 已修复

## 修复后的配置

### CSP白名单域名
- https://www.google-analytics.com
- https://analytics.google.com  
- https://www.googletagmanager.com
- https://hm.baidu.com

### 临时禁用的功能
- ConsoleManager (等待重构后重新启用)

## 下一步计划
1. 重构ConsoleManager避免递归问题
2. 优化CSP策略的动态配置
3. 完善错误处理机制

EOF

echo -e "${GREEN}✅ 修复报告已生成: $REPORT_FILE${NC}"

# 6. 提供启动建议
echo -e "\n${BLUE}🚀 修复完成！建议操作:${NC}"
echo -e "${YELLOW}1. 重新启动开发服务器: npm run dev${NC}"
echo -e "${YELLOW}2. 检查浏览器控制台确认错误已解决${NC}"
echo -e "${YELLOW}3. 验证Google Analytics是否正常工作${NC}"
echo -e "${YELLOW}4. 如有问题，查看修复报告: $REPORT_FILE${NC}"

echo -e "\n${GREEN}🎉 安全功能修复完成！${NC}"
