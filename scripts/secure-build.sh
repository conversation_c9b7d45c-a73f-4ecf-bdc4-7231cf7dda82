#!/bin/bash

# 安全构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查构建环境..."
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    log_info "Node.js 版本: $NODE_VERSION"
    
    # 检查npm版本
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    log_info "npm 版本: $NPM_VERSION"
    
    # 检查环境变量文件
    if [ ! -f ".env.example" ]; then
        log_warning ".env.example 文件不存在"
    fi
    
    log_success "环境检查完成"
}

# 安全检查
security_check() {
    log_info "执行安全检查..."
    
    # 检查是否存在.env文件
    if [ -f ".env" ]; then
        log_warning "发现 .env 文件，请确保不包含敏感信息"
    fi
    
    # 运行安全检查脚本
    if [ -f "scripts/security-check.sh" ]; then
        log_info "运行安全检查脚本..."
        bash scripts/security-check.sh || log_warning "安全检查发现问题，请查看报告"
    else
        log_warning "安全检查脚本不存在"
    fi
    
    log_success "安全检查完成"
}

# 清理构建目录
clean_build() {
    log_info "清理之前的构建文件..."
    
    if [ -d "dist" ]; then
        rm -rf dist
        log_success "已清理 dist 目录"
    fi
    
    if [ -d "build" ]; then
        rm -rf build
        log_success "已清理 build 目录"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    
    # 检查package-lock.json或yarn.lock
    if [ -f "package-lock.json" ]; then
        npm ci
    elif [ -f "yarn.lock" ]; then
        yarn install --frozen-lockfile
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 设置构建环境
    export NODE_ENV=production
    
    # 运行构建命令
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "构建失败：dist 目录不存在"
        exit 1
    fi
    
    # 检查关键文件
    if [ ! -f "dist/index.html" ]; then
        log_error "构建失败：index.html 不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 构建后检查
post_build_check() {
    log_info "执行构建后检查..."
    
    # 检查构建文件大小
    DIST_SIZE=$(du -sh dist | cut -f1)
    log_info "构建文件大小: $DIST_SIZE"
    
    # 检查是否包含敏感信息
    if grep -r "YOUR_SERVICE_ID\|YOUR_TEMPLATE_ID\|YOUR_PUBLIC_KEY" dist/ 2>/dev/null; then
        log_error "构建文件中发现敏感信息占位符"
        exit 1
    fi
    
    # 检查是否包含调试信息
    if grep -r "console\.log\|debugger" dist/ 2>/dev/null; then
        log_warning "构建文件中发现调试信息"
    fi
    
    log_success "构建后检查完成"
}

# Docker构建（可选）
build_docker() {
    if [ "$1" == "--docker" ]; then
        log_info "构建Docker镜像..."
        
        # 检查Dockerfile
        if [ ! -f "Dockerfile" ]; then
            log_error "Dockerfile 不存在"
            exit 1
        fi
        
        # 构建镜像
        docker build -t frost-chain-frontend:latest .
        
        # 推送镜像（如果指定）
        if [ "$2" == "--push" ]; then
            log_info "推送Docker镜像..."
            docker tag frost-chain-frontend:latest starrier/frostchain:latest
            docker push starrier/frostchain:latest
        fi
        
        log_success "Docker镜像构建完成"
    fi
}

# 生成构建报告
generate_report() {
    log_info "生成构建报告..."
    
    REPORT_FILE="build-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
# 构建报告
生成时间: $(date)
项目路径: $(pwd)

## 环境信息
Node.js 版本: $(node -v)
npm 版本: $(npm -v)
操作系统: $(uname -s)

## 构建信息
构建时间: $(date)
构建文件大小: $(du -sh dist | cut -f1)
构建文件数量: $(find dist -type f | wc -l)

## 安全检查
$(if [ -f "security-report-"*.txt ]; then echo "安全报告: $(ls -t security-report-*.txt | head -1)"; else echo "未生成安全报告"; fi)

## 构建文件列表
$(find dist -type f | head -20)
$(if [ $(find dist -type f | wc -l) -gt 20 ]; then echo "... 还有 $(($(find dist -type f | wc -l) - 20)) 个文件"; fi)

EOF

    log_success "构建报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始安全构建流程..."
    
    check_environment
    security_check
    clean_build
    install_dependencies
    build_project
    post_build_check
    build_docker "$@"
    generate_report
    
    log_success "构建流程完成！"
    log_info "构建文件位于 dist/ 目录"
}

# 执行主函数
main "$@"
