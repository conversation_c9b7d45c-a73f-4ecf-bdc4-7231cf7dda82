# 样式系统迁移策略

## 🎯 迁移原则
- **零样式变更**: 保持所有现有样式完全不变
- **多样式系统共存**: 保持现有的多样式解决方案
- **渐进式迁移**: 不破坏现有样式系统

## 🎨 当前样式系统

### 1. CSS Modules
```typescript
// 保持现有使用方式
import styles from './Component.module.css';

const Component = () => (
  <div className={styles.container}>
    <h1 className={styles.title}>标题</h1>
  </div>
);
```

### 2. Tailwind CSS
```typescript
// 保持现有类名使用
<div className="flex items-center justify-center p-4 bg-blue-500">
  <span className="text-white font-bold">内容</span>
</div>
```

### 3. Ant Design
```typescript
// 保持现有主题配置
import { ConfigProvider } from 'antd';
import { useTheme } from '@/theme/hooks/use-theme';

const App = () => {
  const { themeTokens } = useTheme();
  
  return (
    <ConfigProvider theme={themeTokens}>
      {/* 保持现有组件使用 */}
    </ConfigProvider>
  );
};
```

### 4. Vanilla Extract
```typescript
// 保持现有主题系统
import { themeVars } from '@/theme/theme.css';

const styles = style({
  backgroundColor: themeVars.colors.primary,
  color: themeVars.colors.text,
});
```

### 5. SCSS/CSS
```typescript
// 保持现有全局样式
import '@/index.css';
import '@/App.css';
import '@/styles/team.css';
```

## 🔧 Next.js样式配置

### 1. next.config.js样式配置
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用CSS Modules
  cssModules: true,
  
  // 启用SCSS支持
  sassOptions: {
    includePaths: ['./src/styles'],
  },
  
  // Webpack配置保持现有样式处理
  webpack: (config) => {
    // 保持现有的样式加载器配置
    return config;
  },
};
```

### 2. Tailwind配置迁移
```javascript
// tailwind.config.js - 保持现有配置
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // 保持现有的自定义主题
    },
  },
  plugins: [
    // 保持现有的插件
  ],
};
```

### 3. PostCSS配置
```javascript
// postcss.config.js
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    // 保持现有的PostCSS插件
  },
};
```

## 🎭 主题系统保持

### 1. Vanilla Extract主题
```typescript
// app/layout.tsx - 保持主题提供者
import { ThemeProvider } from '@/theme/theme-provider';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### 2. Ant Design主题
```typescript
// 保持现有的Ant Design适配器
import { AntdAdapter } from '@/theme/adapter/antd.adapter';

const App = () => (
  <AntdAdapter>
    {/* 保持现有组件 */}
  </AntdAdapter>
);
```

### 3. 动态主题切换
```typescript
// 保持现有的主题切换逻辑
'use client';
import { useTheme } from '@/theme/hooks/use-theme';

const ThemeToggle = () => {
  const { mode, setMode } = useTheme();
  
  return (
    <button onClick={() => setMode(mode === 'light' ? 'dark' : 'light')}>
      切换主题
    </button>
  );
};
```

## 📱 响应式设计保持

### 1. Bootstrap响应式
```typescript
// 保持现有的Bootstrap类名
<div className="container-fluid">
  <div className="row">
    <div className="col-md-6 col-lg-4">
      内容
    </div>
  </div>
</div>
```

### 2. Tailwind响应式
```typescript
// 保持现有的响应式类名
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  <div className="p-4">内容</div>
</div>
```

### 3. CSS媒体查询
```css
/* 保持现有的媒体查询 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}
```

## 🔄 样式加载优化

### 1. 关键CSS内联
```typescript
// app/layout.tsx
export default function RootLayout({ children }) {
  return (
    <html>
      <head>
        {/* 内联关键CSS */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* 关键样式 */
            body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; }
          `
        }} />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
```

### 2. 样式代码分割
```typescript
// 保持现有的动态导入
const LazyComponent = lazy(() => import('./LazyComponent'));

// LazyComponent.tsx
import styles from './LazyComponent.module.css'; // 自动代码分割
```

### 3. 字体优化
```typescript
// app/layout.tsx
import { Inter } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap', // 优化字体加载
});

export default function RootLayout({ children }) {
  return (
    <html>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
```

## 🎯 迁移检查清单

### ✅ 样式文件迁移
- [ ] 所有CSS文件正常导入
- [ ] CSS Modules正常工作
- [ ] SCSS文件正常编译
- [ ] Tailwind类名正常应用
- [ ] Ant Design主题正常加载

### ✅ 主题系统迁移
- [ ] Vanilla Extract主题正常工作
- [ ] 动态主题切换正常
- [ ] 主题持久化正常
- [ ] 多主题适配器正常

### ✅ 响应式设计迁移
- [ ] 移动端布局正常
- [ ] 平板端布局正常
- [ ] 桌面端布局正常
- [ ] 媒体查询正常工作

### ✅ 性能优化
- [ ] 关键CSS内联
- [ ] 非关键CSS延迟加载
- [ ] 字体优化加载
- [ ] 样式代码分割

## 🚨 注意事项

### 1. 样式优先级
```typescript
// 确保样式优先级保持一致
// 1. 内联样式
// 2. CSS Modules
// 3. Tailwind
// 4. 全局CSS
```

### 2. 服务端渲染样式
```typescript
// 确保样式在服务端正确渲染
// 避免样式闪烁问题
```

### 3. 动态样式
```typescript
// 保持现有的动态样式逻辑
const dynamicStyles = useMemo(() => ({
  backgroundColor: theme.primary,
  color: theme.text,
}), [theme]);
```
