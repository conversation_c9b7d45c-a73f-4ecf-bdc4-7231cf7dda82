# 构建和部署迁移策略

## 🎯 迁移原则
- **保持现有部署流程**: 不改变Docker和Nginx配置
- **保持构建产物**: 确保构建结果一致
- **保持环境配置**: 不改变环境变量和配置

## 🔧 构建配置迁移

### 1. Package.json脚本更新
```json
{
  "scripts": {
    // Next.js脚本
    "dev": "next dev -p 3010",
    "build": "next build",
    "start": "next start -p 3010",
    "export": "next build && next export",
    
    // 保持现有脚本
    "build:prod": "NODE_ENV=production next build",
    "preview": "next start",
    "test": "vitest",
    
    // 保持现有Docker脚本
    "docker:build": "docker build -t frost-chain-frontend:latest .",
    "docker:run": "docker run -d -p 80:80 frost-chain-frontend:latest",
    
    // 保持现有安全脚本
    "security:check": "bash scripts/security-check.sh",
    "build:secure": "bash scripts/secure-build.sh",
    
    // 保持现有配置脚本
    "config:generate": "bash scripts/config-manager.sh generate-env"
  }
}
```

### 2. Next.js构建配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 输出配置 - 保持与Vite一致
  output: 'export', // 静态导出，兼容现有部署
  distDir: 'dist',  // 保持现有输出目录
  trailingSlash: true,
  
  // 图片配置 - 保持现有处理方式
  images: {
    unoptimized: true, // 禁用Next.js图片优化，保持现有方式
  },
  
  // 环境变量 - 保持现有配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // 构建优化 - 保持现有分包策略
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'vendor',
            chunks: 'all',
          },
          antd: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: 'antd',
            chunks: 'all',
          },
          router: {
            test: /[\\/]node_modules[\\/]react-router-dom[\\/]/,
            name: 'router',
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },
};

module.exports = nextConfig;
```

## 🐳 Docker配置保持

### 1. Dockerfile更新
```dockerfile
# 多阶段构建 - 保持现有结构
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./

# 依赖安装阶段
FROM base AS deps
RUN npm ci --only=production

# 构建阶段
FROM base AS builder
COPY . .
RUN npm ci
RUN npm run build

# 生产阶段 - 保持现有Nginx配置
FROM nginx:1.25.3-alpine AS runner
WORKDIR /usr/share/nginx/html

# 复制构建产物
COPY --from=builder /app/dist .

# 保持现有Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 保持现有端口
EXPOSE 80

# 保持现有启动命令
CMD ["nginx", "-g", "daemon off;"]
```

### 2. Docker Compose保持
```yaml
# docker-compose.yml - 完全保持现有配置
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

## 🌐 Nginx配置保持

### 1. 静态文件服务
```nginx
# nginx.conf - 保持现有配置
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;
    
    # 保持现有的静态文件处理
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 保持现有的API代理
    location /api/ {
        proxy_pass https://api.starrier.org/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 保持现有的安全头
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Content-Security-Policy "..." always;
}
```

### 2. 缓存策略保持
```nginx
# 保持现有的缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location ~* \.(html)$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

## 📦 环境配置保持

### 1. 环境变量迁移
```bash
# .env.production - 保持现有配置
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=/api
NEXT_PUBLIC_APP_BASE_PATH=/
NEXT_PUBLIC_USE_MOCK=false
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# 第三方服务配置 - 保持现有
NEXT_PUBLIC_EMAILJS_SERVICE_ID=
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=
```

### 2. 配置生成脚本更新
```javascript
// config/env.config.cjs - 更新环境变量前缀
const nextjsConfig = {
  ...baseConfig,
  // 将VITE_前缀改为NEXT_PUBLIC_前缀
  NEXT_PUBLIC_API_BASE_URL: baseConfig.VITE_API_BASE_URL,
  NEXT_PUBLIC_USE_MOCK: baseConfig.VITE_USE_MOCK,
  // ... 其他配置
};
```

## 🚀 部署流程保持

### 1. CI/CD流程
```yaml
# .github/workflows/deploy.yml - 保持现有流程
name: Deploy
on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      
      # 保持现有的构建步骤
      - run: npm ci
      - run: npm run security:check
      - run: npm run build
      - run: npm run test
      
      # 保持现有的Docker构建
      - run: docker build -t frost-chain-frontend .
      - run: docker push frost-chain-frontend
```

### 2. 健康检查保持
```nginx
# 保持现有的健康检查端点
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}
```

## 📊 性能监控保持

### 1. 构建分析
```javascript
// next.config.js - 添加构建分析
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer(nextConfig);
```

### 2. 运行时监控
```typescript
// instrumentation.ts - 集成现有监控
export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // 集成现有的性能监控
    await import('@/utils/performanceMonitor');
  }
}
```

## 🔄 渐进式部署策略

### 阶段1: 并行部署
```bash
# 保持现有Vite版本运行
# 同时部署Next.js版本到测试环境
docker run -p 8080:80 frost-chain-nextjs:test
```

### 阶段2: 灰度发布
```nginx
# Nginx配置 - 流量分流
upstream vite_backend {
    server vite-app:80 weight=90;
}

upstream nextjs_backend {
    server nextjs-app:80 weight=10;
}

server {
    location / {
        proxy_pass http://vite_backend;
    }
}
```

### 阶段3: 完全切换
```bash
# 验证无问题后完全切换到Next.js
docker stop vite-app
docker start nextjs-app
```

## ✅ 部署检查清单

### 构建验证
- [ ] Next.js构建成功
- [ ] 构建产物大小合理
- [ ] 静态资源正确生成
- [ ] 环境变量正确注入

### 部署验证
- [ ] Docker镜像构建成功
- [ ] 容器正常启动
- [ ] Nginx配置正确加载
- [ ] 健康检查通过

### 功能验证
- [ ] 所有页面正常访问
- [ ] API请求正常工作
- [ ] 静态资源正常加载
- [ ] 安全头正确设置

### 性能验证
- [ ] 页面加载速度正常
- [ ] 资源缓存正常工作
- [ ] 构建产物大小合理
- [ ] 监控数据正常收集
