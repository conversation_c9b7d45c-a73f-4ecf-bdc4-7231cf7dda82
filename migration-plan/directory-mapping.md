# Next.js目录结构迁移映射

## 🎯 迁移原则
- **零功能变更**: 保持所有现有功能完全不变
- **零样式变更**: 保持所有UI和交互完全不变
- **渐进迁移**: 逐步迁移，确保每一步都可回滚

## 📁 目录结构映射

### 当前Vite结构 → Next.js App Router结构

```
当前结构                    →    Next.js结构
─────────────────────────────────────────────────────────

src/                        →    src/
├── pages/                  →    app/
│   ├── Home.tsx           →    ├── page.tsx (首页)
│   ├── About.tsx          →    ├── about/page.tsx
│   ├── Services.tsx       →    ├── services/page.tsx
│   ├── Contact.tsx        →    ├── contact/page.tsx
│   ├── Gallery.tsx        →    ├── gallery/page.tsx
│   └── Team.tsx           →    └── team/page.tsx

├── layouts/                →    app/
│   ├── MainLayout.tsx     →    ├── layout.tsx (根布局)
│   └── TeamLayout.tsx     →    └── team/layout.tsx

├── components/             →    src/components/ (保持不变)
├── features/               →    src/features/ (保持不变)
├── hooks/                  →    src/hooks/ (保持不变)
├── services/               →    src/services/ (保持不变)
├── store/                  →    src/store/ (保持不变)
├── utils/                  →    src/utils/ (保持不变)
├── types/                  →    src/types/ (保持不变)
├── theme/                  →    src/theme/ (保持不变)

├── routes/                 →    删除 (App Router自动路由)
├── App.tsx                 →    app/layout.tsx (根布局)
├── index.tsx               →    删除 (Next.js自动处理)

public/                     →    public/ (保持不变)
```

## 🔄 路由迁移映射

### React Router → App Router

```typescript
// 当前路由配置
<Routes>
  <Route path="/" element={<MainLayout />}>
    <Route index element={<Home />} />
    <Route path="about" element={<About />} />
    <Route path="services" element={<Services />} />
    <Route path="contact" element={<Contact />} />
    <Route path="gallery" element={<Gallery />} />
    <Route path="team" element={<Team />} />
  </Route>
</Routes>

// Next.js App Router结构
app/
├── layout.tsx              // MainLayout
├── page.tsx                // Home
├── about/page.tsx          // About
├── services/page.tsx       // Services
├── contact/page.tsx        // Contact
├── gallery/page.tsx        // Gallery
└── team/
    ├── layout.tsx          // TeamLayout
    └── page.tsx            // Team
```

## 📦 组件迁移策略

### 1. 布局组件迁移
```typescript
// 当前: src/layouts/MainLayout.tsx
export const MainLayout = ({ data }) => {
  return (
    <div className="d-flex flex-column min-vh-100">
      <Navigation />
      <Header data={data.Header} />
      <main>
        <Outlet />
      </main>
      <ContactFloatBar />
      <FooterContact />
    </div>
  );
};

// 迁移后: app/layout.tsx
export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN">
      <body>
        <div className="d-flex flex-column min-vh-100">
          <Navigation />
          <Header />
          <main>
            {children}
          </main>
          <ContactFloatBar />
          <FooterContact />
        </div>
      </body>
    </html>
  );
}
```

### 2. 页面组件迁移
```typescript
// 当前: src/pages/About.tsx
export const About = ({ data }) => {
  return <AboutFeature data={data} />;
};

// 迁移后: app/about/page.tsx
export default function AboutPage() {
  return <AboutFeature />;
}
```

## 🎨 样式系统保持

### CSS Modules
```typescript
// 保持现有的CSS Modules使用方式
import styles from './Component.module.css';
```

### Tailwind CSS
```typescript
// 保持现有的Tailwind类名使用
className="flex items-center justify-center"
```

### Ant Design
```typescript
// 保持现有的Ant Design组件使用
import { Button, Form, Input } from 'antd';
```

### Vanilla Extract
```typescript
// 保持现有的Vanilla Extract主题系统
import { themeVars } from '@/theme/theme.css';
```

## 🔧 状态管理保持

### Zustand
```typescript
// 保持现有的Zustand store
import { useStore } from '@/store';
```

### React Query
```typescript
// 保持现有的React Query使用
import { useQuery } from '@tanstack/react-query';
```

## 🛡️ 安全系统保持

### 安全配置
```typescript
// 保持现有的安全初始化
import { SecurityInit } from '@/utils/securityInit';
```

### CSP配置
```typescript
// 保持现有的CSP管理
import { cspManager } from '@/utils/cspConfig';
```

## 📱 SEO优化增强

### Metadata API
```typescript
// app/layout.tsx
export const metadata = {
  title: '寒链 - 工业冰战略合作伙伴',
  description: '专业的工业冰制造和供应商',
};

// app/about/page.tsx
export const metadata = {
  title: '关于我们 - 寒链',
  description: '了解寒链的发展历程和企业文化',
};
```

## 🚀 性能优化保持

### 代码分割
```typescript
// 保持现有的懒加载
const LazyComponent = lazy(() => import('./Component'));
```

### 图片优化
```typescript
// 使用Next.js Image组件（可选）
import Image from 'next/image';
// 或保持现有的img标签
<img src="/images/hero.jpg" alt="Hero" />
```
