import type { Metadata } from 'next';
import { Inter } from 'next/font/google';

// 保持现有的样式导入
import '@/index.css';
import '@/App.css';

// 保持现有的提供者组件
import { QueryProvider } from '@/providers/QueryProvider';
import { ThemeProvider } from '@/theme/theme-provider';
import { TrackerProvider } from '@/components/TrackerProvider';

// 保持现有的布局组件
import { Navigation } from '@/features/navigation/Navigation';
import { ContactFloatBar } from '@/features/contact-float-bar';
import { FooterContact } from '@/features/contact/FooterContact';
import { CookieConsent } from '@/features/cookie-consent';

// 保持现有的安全初始化
import { SecurityInit } from '@/utils/securityInit';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '寒链 - 工业冰战略合作伙伴',
  description: '专业的工业冰制造和供应商，提供高质量的工业冰产品和服务',
  keywords: '工业冰,制冰,冷链,寒链,冰块制造',
  authors: [{ name: '寒链团队' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: '寒链 - 工业冰战略合作伙伴',
    description: '专业的工业冰制造和供应商',
    type: 'website',
    locale: 'zh_CN',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        {/* 保持现有的第三方脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:3986687,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              var _hmt = _hmt || [];
              (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?518ba7c29aae38e16c0b41d072dd21ae";
                var s = document.getElementsByTagName("script")[0]; 
                s.parentNode.insertBefore(hm, s);
              })();
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        {/* 保持现有的提供者层级结构 */}
        <QueryProvider>
          <ThemeProvider>
            <TrackerProvider>
              {/* 保持现有的安全初始化 */}
              <SecurityInit />
              
              {/* 保持现有的布局结构 */}
              <div className="d-flex flex-column min-vh-100">
                <Navigation />
                
                <main id="main-content" tabIndex={-1} className="flex-grow-1">
                  {children}
                </main>
                
                {/* 保持现有的浮动组件 */}
                <ContactFloatBar />
                <FooterContact />
                <CookieConsent />
              </div>
            </TrackerProvider>
          </ThemeProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
