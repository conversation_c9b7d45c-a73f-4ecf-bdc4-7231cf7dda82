import type { Metadata } from 'next';

// 保持现有的组件导入
import { About } from '@/features/about/About';

// 保持现有的数据获取
import { getStaticData } from '@/data/dataLoader';

export const metadata: Metadata = {
  title: '关于我们 - 寒链',
  description: '了解寒链的发展历程、企业文化和核心价值观',
  openGraph: {
    title: '关于我们 - 寒链',
    description: '了解寒链的发展历程、企业文化和核心价值观',
  },
};

// 服务端数据获取
async function getData() {
  try {
    const data = await getStaticData();
    return data.About || {};
  } catch (error) {
    console.error('Failed to fetch about data:', error);
    return {};
  }
}

export default async function AboutPage() {
  const data = await getData();

  return (
    <>
      {/* 保持现有的About组件完全不变 */}
      <About data={data} />
    </>
  );
}
