import type { Metadata } from 'next';

// 保持现有的组件导入
import { Header } from '@/features/header/Header';
import { Gallery } from '@/features/gallery/Gallery';
import { Features } from '@/features/feature/Features';
import { About } from '@/features/about/About';
import { Services } from '@/features/services/Services';

// 保持现有的数据获取
import { getStaticData } from '@/data/dataLoader';

export const metadata: Metadata = {
  title: '寒链 - 工业冰战略合作伙伴',
  description: '专业的工业冰制造和供应商，提供高质量的工业冰产品和服务',
  openGraph: {
    title: '寒链 - 工业冰战略合作伙伴',
    description: '专业的工业冰制造和供应商',
  },
};

// 服务端数据获取（保持现有数据结构）
async function getData() {
  try {
    // 保持现有的数据获取逻辑
    const data = await getStaticData();
    return data;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    // 返回默认数据，保持现有的错误处理
    return {
      Header: {},
      Gallery: {},
      Features: {},
      About: {},
      Services: {},
    };
  }
}

export default async function HomePage() {
  const data = await getData();

  return (
    <>
      {/* 保持现有的页面结构和组件 */}
      <Header data={data.Header} />
      <Gallery data={data.Gallery} />
      <Features data={data.Features} />
      <About data={data.About} />
      <Services data={data.Services} />
    </>
  );
}
