# 数据获取迁移策略

## 🎯 迁移原则
- **保持现有API**: 不改变任何API调用
- **保持数据结构**: 不改变任何数据格式
- **保持错误处理**: 不改变任何错误处理逻辑
- **保持缓存策略**: 不改变任何缓存机制

## 📊 当前数据获取方式

### 1. 静态数据
```typescript
// 当前方式: src/data/data.json
import jsonData from '@/data/data.json';

// 迁移后: 保持完全相同
import jsonData from '@/data/data.json';
```

### 2. API数据获取
```typescript
// 当前方式: React Query + Axios
const { data, isLoading, error } = useQuery({
  queryKey: ['products'],
  queryFn: () => apiClient.get('/products'),
});

// 迁移后: 保持完全相同的客户端数据获取
// 同时增加服务端预获取（可选）
```

### 3. 表单提交
```typescript
// 当前方式: EmailJS + 自定义验证
const handleSubmit = async (formData) => {
  await emailjs.send(serviceId, templateId, formData);
};

// 迁移后: 保持完全相同
```

## 🔄 Next.js数据获取增强

### 1. 服务端预渲染（SEO优化）
```typescript
// app/page.tsx - 首页预渲染
export default async function HomePage() {
  // 服务端预获取静态数据
  const staticData = await getStaticData();
  
  return (
    <div>
      {/* 预渲染的内容，提升SEO */}
      <Header data={staticData.Header} />
      
      {/* 客户端交互组件保持不变 */}
      <InteractiveComponents />
    </div>
  );
}
```

### 2. 混合渲染策略
```typescript
// 静态内容 - 服务端渲染
const StaticContent = ({ data }) => (
  <div>
    <h1>{data.title}</h1>
    <p>{data.description}</p>
  </div>
);

// 动态内容 - 客户端渲染
'use client';
const DynamicContent = () => {
  const { data } = useQuery(['dynamic-data'], fetchDynamicData);
  return <div>{/* 保持现有的客户端逻辑 */}</div>;
};
```

### 3. 数据获取优化
```typescript
// 并行数据获取
export default async function Page() {
  const [staticData, configData] = await Promise.all([
    getStaticData(),
    getConfigData(),
  ]);
  
  return <PageContent data={staticData} config={configData} />;
}
```

## 🛡️ 错误处理保持

### 1. 客户端错误处理
```typescript
// 保持现有的错误边界
<ErrorBoundary fallback={<ErrorFallback />}>
  <PageContent />
</ErrorBoundary>
```

### 2. 服务端错误处理
```typescript
// app/error.tsx - 新增服务端错误处理
'use client';
export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div>
      <h2>出现了一些问题</h2>
      <button onClick={() => reset()}>重试</button>
    </div>
  );
}
```

## 📱 加载状态保持

### 1. 页面级加载
```typescript
// app/loading.tsx - 页面加载状态
export default function Loading() {
  // 保持现有的加载组件
  return <LoadingSpinner />;
}
```

### 2. 组件级加载
```typescript
// 保持现有的Suspense使用
<Suspense fallback={<ComponentLoading />}>
  <LazyComponent />
</Suspense>
```

## 🔄 渐进式迁移步骤

### 步骤1: 保持现有数据获取
```typescript
// 第一阶段：完全保持现有方式
export default function Page() {
  return (
    <ClientComponent>
      {/* 保持现有的所有数据获取逻辑 */}
    </ClientComponent>
  );
}
```

### 步骤2: 添加服务端预渲染
```typescript
// 第二阶段：添加SEO优化
export default async function Page() {
  const seoData = await getStaticSEOData();
  
  return (
    <div>
      {/* 预渲染的SEO内容 */}
      <SEOContent data={seoData} />
      
      {/* 保持现有的客户端逻辑 */}
      <ClientComponent />
    </div>
  );
}
```

### 步骤3: 优化性能
```typescript
// 第三阶段：性能优化
export default async function Page() {
  const criticalData = await getCriticalData();
  
  return (
    <div>
      {/* 关键内容立即渲染 */}
      <CriticalContent data={criticalData} />
      
      {/* 非关键内容延迟加载 */}
      <Suspense fallback={<Loading />}>
        <NonCriticalContent />
      </Suspense>
    </div>
  );
}
```

## 📈 性能监控保持

### 1. 保持现有监控
```typescript
// 保持现有的性能监控
import { PerformanceMonitor } from '@/utils/performanceMonitor';

export default function Page() {
  return (
    <PerformanceMonitor>
      <PageContent />
    </PerformanceMonitor>
  );
}
```

### 2. 增加Next.js监控
```typescript
// next.config.js
module.exports = {
  experimental: {
    instrumentationHook: true,
  },
};

// instrumentation.ts
export async function register() {
  // 集成现有的监控系统
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('./monitoring/server');
  }
}
```
