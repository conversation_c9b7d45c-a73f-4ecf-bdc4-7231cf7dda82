# 开发环境覆盖配置
version: '3.8'

services:
  frontend:
    build:
      args:
        - NODE_ENV=development
    ports:
      - "3010:8080"
    environment:
      NODE_ENV: development
      VITE_USE_MOCK: true
      VITE_ENABLE_DEBUG: true
      VITE_ENABLE_ANALYTICS: false
      API_BASE_URL: http://localhost:3000/api
    volumes:
      # 开发环境挂载源码以支持热重载
      - ./src:/app/src:ro
      - ./public:/app/public:ro
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    
  # 开发环境的模拟后端服务
  mock-backend:
    image: node:20-alpine
    container_name: frost-chain-mock-backend
    working_dir: /app
    volumes:
      - ./mock-server:/app
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
    command: sh -c "npm install && npm start"
    networks:
      - frost-chain-network
