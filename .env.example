# 环境变量示例文件
# 复制此文件为 .env 并填入实际值

# ===========================================
# 基础配置
# ===========================================

# 应用环境 (development | production | test)
NODE_ENV=production

# API基础URL
VITE_API_BASE_URL=/api

# 应用基础路径
VITE_APP_BASE_PATH=/

# 是否启用Mock数据 (true | false)
VITE_USE_MOCK=false

# ===========================================
# 第三方服务配置 (生产环境请从后端API获取)
# ===========================================

# EmailJS配置 (请替换为实际值)
VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here  
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here

# 分析服务配置
VITE_ANALYTICS_API=https://api.starrier.org/analytics
VITE_ANALYTICS_TRACKING_ID=your_tracking_id_here

# ===========================================
# 安全配置
# ===========================================

# CSRF保护密钥 (生产环境请使用强随机值)
VITE_CSRF_SECRET=your_csrf_secret_here

# 会话超时时间 (分钟)
VITE_SESSION_TIMEOUT=30

# API请求超时时间 (毫秒)
VITE_REQUEST_TIMEOUT=10000

# 最大重试次数
VITE_MAX_RETRY_ATTEMPTS=3

# ===========================================
# 功能开关
# ===========================================

# 是否启用错误报告
VITE_ENABLE_ERROR_REPORTING=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=true

# 是否启用用户行为分析
VITE_ENABLE_ANALYTICS=true

# 是否启用调试模式
VITE_ENABLE_DEBUG=false

# ===========================================
# CDN和外部资源配置
# ===========================================

# CDN基础URL
VITE_CDN_BASE_URL=https://cdn.jsdelivr.net

# 字体CDN URL
VITE_FONT_CDN_URL=https://fonts.googleapis.com

# ===========================================
# 开发环境专用配置
# ===========================================

# 开发服务器端口
VITE_DEV_PORT=3000

# 开发服务器主机
VITE_DEV_HOST=localhost

# 是否启用热重载
VITE_HMR=true

# ===========================================
# 生产环境专用配置
# ===========================================

# 生产环境域名
VITE_PROD_DOMAIN=frologi.com

# 是否启用HTTPS
VITE_ENABLE_HTTPS=true

# 是否启用Gzip压缩
VITE_ENABLE_GZIP=true

# ===========================================
# 安全注意事项
# ===========================================

# 1. 请勿在代码中硬编码敏感信息
# 2. 生产环境的敏感配置应通过安全的方式注入
# 3. 定期轮换API密钥和令牌
# 4. 使用强随机值作为密钥
# 5. 限制环境变量的访问权限

# ===========================================
# 示例值说明
# ===========================================

# your_service_id_here: 替换为EmailJS服务ID
# your_template_id_here: 替换为EmailJS模板ID  
# your_public_key_here: 替换为EmailJS公钥
# your_tracking_id_here: 替换为分析服务跟踪ID
# your_csrf_secret_here: 替换为CSRF保护密钥
