# 统一的 Docker Compose 配置
# 支持多环境部署，通过环境变量和覆盖文件控制

version: '3.8'

# 定义可重用的配置片段
x-common-variables: &common-variables
  NODE_ENV: ${NODE_ENV:-production}
  API_BASE_URL: ${API_BASE_URL:-/api}
  APP_BASE_PATH: ${APP_BASE_PATH:-/}

x-frontend-base: &frontend-base
  build:
    context: .
    dockerfile: Dockerfile
    args:
      - NODE_ENV=${NODE_ENV:-production}
  image: frost-chain-frontend:${IMAGE_TAG:-latest}
  restart: unless-stopped
  environment:
    <<: *common-variables
  healthcheck:
    test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

services:
  # 前端服务
  frontend:
    <<: *frontend-base
    container_name: frost-chain-frontend-${ENV:-prod}
    ports:
      - "${FRONTEND_PORT:-80}:8080"
    environment:
      <<: *common-variables
      # 生产环境特定配置
      VITE_ENABLE_DEBUG: ${VITE_ENABLE_DEBUG:-false}
      VITE_USE_MOCK: ${VITE_USE_MOCK:-false}
      VITE_ENABLE_ANALYTICS: ${VITE_ENABLE_ANALYTICS:-true}
    networks:
      - frost-chain-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=8080"

  # 可选：反向代理服务（生产环境推荐）
  nginx-proxy:
    image: nginx:1.25.3-alpine
    container_name: frost-chain-proxy-${ENV:-prod}
    ports:
      - "${PROXY_PORT:-443}:443"
      - "${HTTP_PORT:-80}:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
    networks:
      - frost-chain-network
    profiles:
      - proxy
    restart: unless-stopped

  # 可选：监控服务
  monitoring:
    image: prom/prometheus:latest
    container_name: frost-chain-monitoring-${ENV:-prod}
    ports:
      - "${MONITORING_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - frost-chain-network
    profiles:
      - monitoring
    restart: unless-stopped

networks:
  frost-chain-network:
    driver: bridge
    name: frost-chain-${ENV:-prod}

volumes:
  nginx-cache:
    name: frost-chain-nginx-cache-${ENV:-prod}
  monitoring-data:
    name: frost-chain-monitoring-${ENV:-prod}
