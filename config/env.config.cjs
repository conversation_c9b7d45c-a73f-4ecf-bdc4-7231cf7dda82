/**
 * 统一环境配置管理
 * 替代多个 .env 文件，提供集中化的环境配置
 */

// 基础配置
const baseConfig = {
  // API配置
  VITE_API_BASE_URL: '/api',
  VITE_APP_BASE_PATH: '/',
  
  // 功能开关
  VITE_USE_MOCK: false,
  VITE_ENABLE_ERROR_REPORTING: true,
  VITE_ENABLE_PERFORMANCE_MONITORING: true,
  VITE_ENABLE_ANALYTICS: true,
  VITE_ENABLE_DEBUG: false,
  
  // 安全配置
  VITE_REQUEST_TIMEOUT: 10000,
  VITE_MAX_RETRY_ATTEMPTS: 3,
  VITE_SESSION_TIMEOUT: 30,
  
  // CDN配置
  VITE_CDN_BASE_URL: 'https://cdn.jsdelivr.net',
  VITE_FONT_CDN_URL: 'https://fonts.googleapis.com',
  
  // 第三方服务配置（占位符，实际值应从环境变量或安全API获取）
  VITE_EMAILJS_SERVICE_ID: '',
  VITE_EMAILJS_TEMPLATE_ID: '',
  VITE_EMAILJS_PUBLIC_KEY: '',
  VITE_ANALYTICS_API: 'https://api.starrier.org/analytics',
  VITE_ANALYTICS_TRACKING_ID: '',
};

// 开发环境配置
const developmentConfig = {
  ...baseConfig,
  NODE_ENV: 'development',
  VITE_USE_MOCK: true,
  VITE_ENABLE_DEBUG: true,
  VITE_DEV_PORT: 3010,
  VITE_DEV_HOST: 'localhost',
  VITE_HMR: true,
};

// 测试环境配置
const testConfig = {
  ...baseConfig,
  NODE_ENV: 'test',
  VITE_USE_MOCK: true,
  VITE_ENABLE_ANALYTICS: false,
  VITE_ENABLE_ERROR_REPORTING: false,
  VITE_API_BASE_URL: '/api/test',
};

// 生产环境配置
const productionConfig = {
  ...baseConfig,
  NODE_ENV: 'production',
  VITE_USE_MOCK: false,
  VITE_ENABLE_DEBUG: false,
  VITE_PROD_DOMAIN: 'frologi.com',
  VITE_ENABLE_HTTPS: true,
  VITE_ENABLE_GZIP: true,
};

// Docker环境配置
const dockerConfig = {
  ...productionConfig,
  VITE_CONTAINER_MODE: true,
  VITE_HEALTH_CHECK_ENDPOINT: '/health',
};

// 本地环境配置
const localConfig = {
  ...developmentConfig,
  NODE_ENV: 'development',
  VITE_API_BASE_URL: 'http://localhost:8080/api',
  VITE_USE_MOCK: true,
};

// 环境配置映射
const envConfigs = {
  development: developmentConfig,
  test: testConfig,
  production: productionConfig,
  docker: dockerConfig,
  local: localConfig,
};

/**
 * 获取当前环境配置
 */
function getEnvConfig(env = 'development') {
  const config = envConfigs[env] || envConfigs.development;
  
  // 从实际环境变量中覆盖配置
  const envOverrides = {};
  Object.keys(config).forEach(key => {
    if (process.env[key] !== undefined) {
      envOverrides[key] = process.env[key];
    }
  });
  
  return {
    ...config,
    ...envOverrides,
  };
}

/**
 * 生成 .env 文件内容
 */
function generateEnvFile(env = 'development') {
  const config = getEnvConfig(env);
  const lines = Object.entries(config).map(([key, value]) => {
    const stringValue = typeof value === 'boolean' ? value.toString() : 
                       typeof value === 'number' ? value.toString() : 
                       value || '';
    return `${key}=${stringValue}`;
  });
  
  return [
    `# 自动生成的 ${env} 环境配置`,
    `# 生成时间: ${new Date().toISOString()}`,
    '',
    ...lines,
    '',
    '# 注意：此文件由 config/env.config.cjs 自动生成',
    '# 请勿手动编辑，修改请在 env.config.cjs 中进行',
  ].join('\n');
}

/**
 * 验证环境配置
 */
function validateConfig(config) {
  const errors = [];
  const warnings = [];
  
  // 必需的配置项
  const requiredKeys = [
    'NODE_ENV',
    'VITE_API_BASE_URL',
    'VITE_APP_BASE_PATH',
  ];
  
  requiredKeys.forEach(key => {
    if (!config[key]) {
      errors.push(`缺少必需的配置项: ${key}`);
    }
  });
  
  // 生产环境特殊检查
  if (config.NODE_ENV === 'production') {
    if (config.VITE_ENABLE_DEBUG) {
      warnings.push('生产环境不应启用调试模式');
    }
    
    if (config.VITE_USE_MOCK) {
      warnings.push('生产环境不应使用Mock数据');
    }
    
    if (!config.VITE_EMAILJS_SERVICE_ID) {
      warnings.push('生产环境应配置EmailJS服务ID');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// 导出配置
module.exports = {
  baseConfig,
  developmentConfig,
  testConfig,
  productionConfig,
  dockerConfig,
  localConfig,
  envConfigs,
  getEnvConfig,
  generateEnvFile,
  validateConfig,
};

// 如果直接运行此文件，生成对应的 .env 文件
if (require.main === module) {
  const fs = require('fs');
  const path = require('path');
  
  const env = process.argv[2] || 'development';
  const outputPath = process.argv[3] || `.env.${env}`;
  
  try {
    const envContent = generateEnvFile(env);
    const fullPath = path.resolve(process.cwd(), outputPath);
    
    fs.writeFileSync(fullPath, envContent);
    console.log(`✅ 已生成 ${env} 环境配置文件: ${fullPath}`);
    
    // 验证配置
    const config = getEnvConfig(env);
    const validation = validateConfig(config);
    
    if (validation.warnings.length > 0) {
      console.log('\n⚠️  配置警告:');
      validation.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    if (!validation.isValid) {
      console.log('\n❌ 配置错误:');
      validation.errors.forEach(error => console.log(`  - ${error}`));
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 生成配置文件失败:', error.message);
    process.exit(1);
  }
}
