# 自动生成的 test 环境配置
# 生成时间: 2025-06-04T06:16:12.400Z

VITE_API_BASE_URL=/api/test
VITE_APP_BASE_PATH=/
VITE_USE_MOCK=true
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=false
VITE_REQUEST_TIMEOUT=10000
VITE_MAX_RETRY_ATTEMPTS=3
VITE_SESSION_TIMEOUT=30
VITE_CDN_BASE_URL=https://cdn.jsdelivr.net
VITE_FONT_CDN_URL=https://fonts.googleapis.com
VITE_EMAILJS_SERVICE_ID=
VITE_EMAILJS_TEMPLATE_ID=
VITE_EMAILJS_PUBLIC_KEY=
VITE_ANALYTICS_API=https://api.starrier.org/analytics
VITE_ANALYTICS_TRACKING_ID=
NODE_ENV=test

# 注意：此文件由 config/env.config.cjs 自动生成
# 请勿手动编辑，修改请在 env.config.cjs 中进行