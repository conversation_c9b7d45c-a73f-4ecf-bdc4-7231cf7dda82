.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
  font-size: 2rem;
}

.tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.tab {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  background: #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;

  &:hover {
    background: #e0e0e0;
  }

  &.active {
    background: #1890ff;
    color: white;
  }
}

.caseList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.imageContainer {
  height: 200px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  .card:hover & {
    transform: scale(1.05);
  }
}

.info {
  padding: 1.5rem;
}

.clientName {
  margin: 0 0 0.5rem;
  color: #333;
  font-size: 1.2rem;
}

.industry {
  margin: 0 0 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.scenario {
  margin: 0;
  color: #888;
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .caseList {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 1rem;
  }
}
