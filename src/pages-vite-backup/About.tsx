import React from 'react';
import { Helmet } from 'react-helmet-async';

interface AboutProps {
  data?: any;
}

const About: React.FC<AboutProps> = () => {
  return (
    <>
      <Helmet>
        <title>关于我们 | 上海寒链实业有限公司</title>
        <meta name="description" content="了解上海寒链实业有限公司的历史、使命和价值观。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">关于我们</h1>
              <p className="lead">了解我们的历史和使命</p>
            </div>
          </div>
        </div>
      </div>

      <section className="py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mx-auto">
              <h2 className="mb-4">我们的故事</h2>
              <p>
                上海寒链实业有限公司成立于2010年，是一家专注于冷链物流和食用冰生产的企业。
                多年来，我们不断创新和发展，已成为行业内的领先企业。
              </p>
              <p>
                我们的团队由行业专家组成，拥有丰富的经验和专业知识，致力于为客户提供最优质的产品和服务。
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default About;