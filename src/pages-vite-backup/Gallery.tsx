import React from 'react';
import { Helmet } from 'react-helmet-async';

interface GalleryProps {
  data?: any;
}

const Gallery: React.FC<GalleryProps> = ({ data }) => {
  return (
    <>
      <Helmet>
        <title>图片展示 | 上海寒链实业有限公司</title>
        <meta name="description" content="浏览上海寒链实业有限公司的设施、产品和服务图片。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">图片展示</h1>
              <p className="lead">探索我们的设施和产品</p>
            </div>
          </div>
        </div>
      </div>

      <section className="py-5">
        <div className="container">
          <div className="row g-4">
            {data && data.length > 0 ? (
              data.map((item: any, index: number) => (
                <div key={index} className="col-md-6 col-lg-4">
                  <div className="card h-100 shadow-sm">
                    <img 
                      src={item.img} 
                      className="card-img-top" 
                      alt={item.title}
                      style={{ height: '250px', objectFit: 'cover' }}
                    />
                    <div className="card-body">
                      <h5 className="card-title">{item.title}</h5>
                      <p className="card-text">{item.description}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12 text-center">
                <p>暂无图片</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default Gallery;