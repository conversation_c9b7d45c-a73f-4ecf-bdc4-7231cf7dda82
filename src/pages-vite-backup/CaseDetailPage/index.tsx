import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './styles.module.scss';

// 复用CaseItem接口和模拟数据
import { CaseItem, caseData } from '../CaseShowcasePage';

const CaseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const caseItem = caseData.find(item => item.id === id);

  if (!caseItem) {
    return <div className={styles.notFound}>案例不存在</div>;
  }

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>{caseItem.clientName}案例详情</h1>

      <div className={styles.content}>
        <div className={styles.mainImage}>
          <img src={caseItem.thumbnail} alt={caseItem.clientName} />
        </div>

        <div className={styles.details}>
          <h2>{caseItem.clientName}</h2>
          <p><strong>行业:</strong> {caseItem.industry}</p>
          <p><strong>应用场景:</strong> {caseItem.scenario}</p>
          <p className={styles.description}>{caseItem.description}</p>
        </div>

        {caseItem.images && caseItem.images.length > 0 && (
          <div className={styles.gallery}>
            <h3>案例图片</h3>
            <div className={styles.imageGrid}>
              {caseItem.images.map((image, index) => (
                <div key={index} className={styles.imageItem}>
                  <img src={image} alt={`案例图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CaseDetailPage;
