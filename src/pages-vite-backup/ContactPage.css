.contact-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 覆盖TeamLayout的边距设置 */
.contact-page-wrapper {
  margin-top: -1rem !important; /* 调整与导航栏的间距 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 页面标题区域 */
.contact-header {
  padding: 20px 0 30px;
  background-color: #f5f7fa;
  text-align: center;
}

.contact-header h1 {
  font-size: 36px;
  margin-bottom: 0;
}

/* 内容区域 */
.contact-content {
  flex: 1;
  padding: 40px 0;
}

/* 区块标题 */
.section-title {
  position: relative;
  margin-bottom: 30px !important;
  text-align: center;
  font-size: 24px !important;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #1890ff;
}

/* 地址区域 */
.address-section {
  margin-bottom: 60px;
}

.address-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
  overflow: hidden;
}

.address-info {
  padding-right: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.info-icon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 15px;
  margin-top: 3px;
}

.info-content {
  display: flex;
  flex-direction: column;
}

/* 地图区域 */
.map-container {
  height: 100%;
  min-height: 250px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
}

.map-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 15px;
}

.map-actions {
  position: absolute;
  bottom: 15px;
  left: 15px;
  display: flex;
  gap: 10px;
}

.map-link {
  background-color: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  text-decoration: none;
  transition: all 0.3s;
}

.map-link:hover {
  background-color: #1890ff;
  color: white;
}

/* 二维码区域 */
.qrcode-section {
  margin-bottom: 60px;
}

.qrcode-container {
  padding: 20px 0;
}

.qrcode-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.qrcode-card:hover {
  transform: translateY(-5px);
}

.qrcode-image {
  width: 150px !important;
  height: 150px !important;
  object-fit: cover;
  margin-bottom: 15px;
}

.qrcode-info {
  margin-top: 10px;
}

.qrcode-title {
  display: block;
  margin-bottom: 5px;
}

.qrcode-desc {
  font-size: 12px;
}

/* 社交媒体链接 */
.social-section {
  margin-bottom: 40px;
  text-align: center;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f2f5;
  color: #666;
  font-size: 20px;
  transition: all 0.3s;
}

.social-link:hover {
  background-color: #1890ff;
  color: white;
}

/* 页脚 */
.contact-footer {
  background-color: #f5f7fa;
  padding: 20px 0;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contact-header {
    padding: 60px 0 30px;
  }

  .contact-header h1 {
    font-size: 28px;
  }

  .address-info {
    padding-right: 0;
    margin-bottom: 20px;
  }

  .map-container {
    min-height: 200px;
  }

  .qrcode-image {
    width: 120px !important;
    height: 120px !important;
  }

  .map-actions {
    flex-direction: column;
    gap: 5px;
  }
}
