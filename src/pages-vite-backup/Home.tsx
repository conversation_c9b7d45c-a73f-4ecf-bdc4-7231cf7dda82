import React from 'react';
import { Link } from 'react-router-dom';
import { SEO, CompanySchema } from '../components/seo';
import ProductShowcase from '../features/home/<USER>';

interface HomeProps {
  data?: any;
}

const Home: React.FC<HomeProps> = ({ data }) => {
  return (
    <>
      <SEO
        title="上海寒链实业有限公司 | 专业冷链解决方案"
        description="上海寒链实业有限公司提供专业的冷链物流和食用冰生产服务，满足各行业客户需求。"
        keywords="工业用冰,冷链解决方案,食品保鲜,工地降温"
        ogType="website"
        ogImage="/img/og-image.jpg"
        ogImageAlt="上海寒链实业有限公司"
      />
      <CompanySchema />

      {/* 产品展示组件 */}
      <ProductShowcase />

      {/* 添加一个测试链接 */}
      <div className="container my-5 text-center">
        <h3>测试路由链接</h3>
        <Link to="/team" className="btn btn-primary me-3">
          React Router 链接到团队页面
        </Link>
        <a href="/team" className="btn btn-secondary me-3">
          普通链接到团队页面
        </a>
        <a href="/team" target="_blank" rel="noopener noreferrer" className="btn btn-info">
          在新标签页打开团队页面
        </a>
      </div>
    </>
  );
};

export default Home;