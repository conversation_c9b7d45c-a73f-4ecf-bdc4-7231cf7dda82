import React from 'react';
import { createRoot } from 'react-dom/client';
import { HelmetProvider } from 'react-helmet-async';
import App from './App';
import { startMockService } from './services/api/mockService';
import * as serviceWorker from './serviceWorker';
import { initPerformanceMonitoring } from './utils/performanceMonitor';
import { initializeSecurity } from './utils/securityInit';
import env, { isDev } from './utils/env';
import './index.css';
import './styles/a11y.css'; // 可访问性样式

// 应用初始化函数
async function initializeApp() {
  try {
    // 1. 初始化安全配置
    await initializeSecurity();

    // 2. 确保在控制台显示当前环境配置
    console.log('Environment:', {
      NODE_ENV: env.NODE_ENV,
      API_BASE_URL: env.API_BASE_URL,
      APP_BASE_PATH: env.APP_BASE_PATH,
    });

    // 3. 初始化性能监控
    const performanceMonitor = initPerformanceMonitoring(
      (metrics) => {
        // 在生产环境中可以将指标发送到分析服务
        if (env.NODE_ENV === 'production') {
          console.log('Performance metrics:', metrics);
          // 可以将指标发送到分析服务
          // sendToAnalyticsService(metrics);
        }
      },
      isDev // 在开发环境中启用调试模式
    );

    // 4. 在开发环境启动模拟服务
    if (isDev && (import.meta.env.VITE_USE_MOCK === 'true' || env.USE_MOCK === 'true')) {
      startMockService();
    }

    // 5. 渲染应用
    renderApp();

  } catch (error) {
    console.error('应用初始化失败:', error);
    // 显示错误页面或降级处理
    renderErrorPage(error);
  }
}

// 渲染应用函数
function renderApp() {
  const container = document.getElementById('react');
  if (!container) throw new Error('容器元素未找到');

  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <HelmetProvider>
        <App />
      </HelmetProvider>
    </React.StrictMode>
  );
}

// 渲染错误页面
function renderErrorPage(error: any) {
  const container = document.getElementById('react');
  if (!container) return;

  const root = createRoot(container);
  root.render(
    <div style={{
      padding: '20px',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif',
      color: '#721c24',
      backgroundColor: '#f8d7da',
      border: '1px solid #f5c6cb',
      borderRadius: '4px',
      margin: '20px'
    }}>
      <h2>应用初始化失败</h2>
      <p>抱歉，应用无法正常启动。请刷新页面重试。</p>
      {isDev && (
        <details style={{ marginTop: '10px', textAlign: 'left' }}>
          <summary>错误详情（开发模式）</summary>
          <pre style={{
            backgroundColor: '#f8f9fa',
            padding: '10px',
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {error?.message || String(error)}
          </pre>
        </details>
      )}
    </div>
  );
}

// 启动应用
initializeApp();

// 如果需要离线支持，可以将 unregister 改为 register
serviceWorker.unregister();