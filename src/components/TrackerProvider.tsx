import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { SecureUserIdGenerator } from '../utils/secureConfig';

interface TrackerContextType {
  trackEvent: (eventName: string, eventData?: Record<string, any>) => void;
  trackPageView: (pageName: string, pageData?: Record<string, any>) => void;
}

interface TrackerProviderProps {
  children: ReactNode;
}

// 获取设备基本信息
const getDeviceInfo = () => ({
  userAgent: navigator.userAgent,
  screenWidth: window.screen.width,
  screenHeight: window.screen.height,
  language: navigator.language,
});

// 生成唯一会话ID
const generateSessionId = () => {
  return 'session_' + Math.random().toString(36).substring(2, 15);
};

const TrackerContext = createContext<TrackerContextType>({
  trackEvent: () => {},
  trackPageView: () => {},
});

export const useTracker = (): TrackerContextType => {
  return useContext(TrackerContext);
};

export const TrackerProvider: React.FC<TrackerProviderProps> = ({ children }) => {
  const sessionId = React.useRef(generateSessionId());
  const userId = React.useRef(SecureUserIdGenerator.getUserId());

  // 初始化时设置用户ID（已在SecureUserIdGenerator中处理）
  useEffect(() => {
    // 用户ID已经在SecureUserIdGenerator中安全生成和存储
  }, []);

  // 获取API端点基础URL
  const getApiBaseUrl = () => {
    // 优先使用环境变量配置
    if (process.env.REACT_APP_ANALYTICS_API) {
      return process.env.REACT_APP_ANALYTICS_API;
    }
    // 默认开发环境地址
    return process.env.NODE_ENV === 'production' 
      ? 'https://api.starrier.org/analytics'
      : 'http://localhost:808/api/analysics';
  };

  // 上报数据到分析接口
  const sendToAnalytics = async (data: any) => {
    const baseUrl = getApiBaseUrl();
    
    try {
      // 开发环境下检查接口健康状态
      if (process.env.NODE_ENV === 'development') {
        const healthCheck = await fetch(`${baseUrl}/health`, { method: 'GET' });
        if (!healthCheck.ok) {
          console.warn('分析接口不可用，数据将记录到控制台');
          console.log('[Analytics Data]', data);
          return;
        }
      }

      // 上报数据
      const response = await fetch(`${baseUrl}/data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok && process.env.NODE_ENV === 'development') {
        console.warn('数据上报失败:', response.status);
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('分析服务不可达:', error);
        console.log('[Analytics Data]', data);
      }
      // 生产环境静默失败
    }
  };

  const trackEvent = (eventName: string, eventData: Record<string, any> = {}) => {
    const payload = {
      eventType: 'event',
      eventName,
      timestamp: new Date().toISOString(),
      sessionId: sessionId.current,
      userId: userId.current,
      deviceInfo: getDeviceInfo(),
      url: window.location.href,
      ...eventData,
    };
    
    // 使用requestIdleCallback避免阻塞主线程
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => sendToAnalytics(payload));
    } else {
      setTimeout(() => sendToAnalytics(payload), 0);
    }
  };

  const trackPageView = (pageName: string, pageData: Record<string, any> = {}) => {
    const payload = {
      eventType: 'pageview',
      pageName,
      timestamp: new Date().toISOString(),
      sessionId: sessionId.current,
      userId: userId.current,
      deviceInfo: getDeviceInfo(),
      url: window.location.href,
      referrer: document.referrer,
      ...pageData,
    };
    
    sendToAnalytics(payload);
  };

  return (
    <TrackerContext.Provider value={{ trackEvent, trackPageView }}>
      {children}
    </TrackerContext.Provider>
  );
};

export default TrackerProvider;