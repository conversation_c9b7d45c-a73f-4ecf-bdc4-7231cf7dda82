import { CarouselItem, CarouselService } from './types';
import { defaultCarouselItems } from './carouselData';

/**
 * API基础URL
 * 如果环境变量中没有定义，则使用默认值
 */
const getApiBaseUrl = (): string => {
  // Next.js环境变量
  if (typeof process !== 'undefined' && process.env) {
    return process.env.VITE_API_BASE_URL || process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  }

  // Vite环境变量（仅在客户端可用）
  if (typeof window !== 'undefined' && import.meta?.env) {
    return import.meta.env.VITE_API_BASE_URL || '/api';
  }

  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

/**
 * 轮播数据服务实现
 * 提供获取轮播数据的方法
 */
class CarouselServiceImpl implements CarouselService {
  /**
   * 获取轮播项目列表
   * 尝试从API获取数据，如果失败则使用默认数据
   * 
   * @returns 轮播项目数组的Promise
   */
  async getCarouselItems(): Promise<CarouselItem[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/carousel`);
      
      if (!response.ok) {
        console.warn('无法从API获取轮播数据，使用默认数据');
        return defaultCarouselItems;
      }
      
      const data = await response.json();
      return data as CarouselItem[];
    } catch (error) {
      console.error('获取轮播数据时出错:', error);
      // 返回默认数据作为备选
      return defaultCarouselItems;
    }
  }

  /**
   * 根据ID获取特定轮播项目
   * 
   * @param id - 轮播项目ID
   * @returns 轮播项目的Promise，如果未找到则返回null
   */
  async getCarouselItemById(id: number): Promise<CarouselItem | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/carousel/${id}`);
      
      if (!response.ok) {
        // 从默认数据中查找
        const item = defaultCarouselItems.find(item => item.id === id);
        return item || null;
      }
      
      const data = await response.json();
      return data as CarouselItem;
    } catch (error) {
      console.error(`获取ID为${id}的轮播项目时出错:`, error);
      // 从默认数据中查找
      const item = defaultCarouselItems.find(item => item.id === id);
      return item || null;
    }
  }
}

/**
 * 导出轮播服务实例
 */
export const carouselService = new CarouselServiceImpl();
