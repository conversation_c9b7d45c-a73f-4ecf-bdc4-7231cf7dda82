import React from 'react';
import { useNavigate } from 'react-router-dom';
import { NavBrand } from './NavBrand';
import { NavToggler } from './NavToggler';
import { NavCollapse } from './NavCollapse';
import { NavigationProps, NavItem } from './types';
import { SecureNavigation } from '../../utils/hijackingProtection';
import './Navigation.css'; // 导入导航样式修复

/**
 * 导航项目数据
 */
const NAV_ITEMS: NavItem[] = [
  { href: '/', text: '首页', isAnchor: false },
  { href: '/product', text: '产品信息', isAnchor: false },
  { href: '/cases', text: '客户案例', isAnchor: false },
  { href: '/team', text: '团队', isAnchor: false },
  { href: '/contact', text: '联系我们', isAnchor: false },
];

/**
 * 导航组件
 * 显示网站顶部导航栏，包含品牌和导航链接
 *
 * @param props - 导航组件属性
 * @returns React组件
 */
export const Navigation: React.FC<NavigationProps> = ({ brandName = '寒链' }) => {
  // 使用 try-catch 来安全地使用 useNavigate
  let navigate: ((path: string) => void) | undefined;
  // 删除未使用的 location 变量

  try {
    navigate = useNavigate();
    // 如果需要使用 location，可以在这里解除注释
    // const location = useLocation();
  } catch (error) {
    console.warn('Router context not available, falling back to window.location');
    // 如果 useNavigate 不可用，提供一个备用函数
    navigate = (path: string) => {
      SecureNavigation.navigateTo(path);
    };
  }

  const collapseId = "navbarCollapse";

  /**
   * 处理导航项点击事件
   *
   * @param e - 点击事件
   * @param item - 导航项
   */
  const handleNavItemClick = (e: React.MouseEvent<HTMLAnchorElement>, item: NavItem): void => {
    console.log('点击了导航项:', item);

    if (item.isAnchor) {
      // 锚点链接，使用默认行为
      console.log('这是锚点链接');
    } else {
      // 路由链接，使用编程式导航或回退到普通导航
      console.log('这是路由链接，将导航到:', item.href);
      e.preventDefault();

      if (typeof navigate === 'function') {
        navigate(item.href);
      } else {
        // 回退方案：使用安全导航
        SecureNavigation.navigateTo(item.href);
      }
    }
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-light fixed-top">
      <div className="container">
        <NavBrand brandName={brandName} href="#page-top" />
        <NavToggler targetId={collapseId} />

        <NavCollapse id={collapseId}>
          <ul className="navbar-nav ms-auto mb-2 mb-lg-0">
            {NAV_ITEMS.map((item, index) => (
              <li key={index} className="nav-item">
                <a
                  href={item.href}
                  className={`nav-link ${item.isAnchor ? 'page-scroll' : ''}`}
                  onClick={(e) => handleNavItemClick(e, item)}
                >
                  {item.text}
                </a>
              </li>
            ))}
          </ul>
        </NavCollapse>
      </div>
    </nav>
  );
};
