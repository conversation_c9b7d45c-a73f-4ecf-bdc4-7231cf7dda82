import { http, HttpResponse } from 'msw';
import { setupWorker } from 'msw/browser';
import { UserApiEndpoints } from './services/userService';
import { FeatureApiEndpoints } from '@/services/api/services';
import { ProductApiEndpoints } from './services/productService';
import { products as mockProducts } from '@/features/product/data/productData';
import { getApiBaseUrl, isMockEnabled, isDevelopment } from '@/utils/envCompat';

// 使用兼容的环境变量工具
const API_BASE_URL = getApiBaseUrl();
const USE_MOCK = isMockEnabled();
const IS_DEV = isDevelopment();

// 模拟用户数据
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    role: 'admin',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
];

// 模拟特性数据
const mockFeatures = [
  {
    title: '高性能',
    text: '采用先进技术，确保系统高效运行',
  },
  {
    title: '安全可靠',
    text: '多重安全机制，保障数据安全',
  },
  {
    title: '24/7 服务',
    text: '全天候技术支持，随时解决问题',
  },
  {
    title: '智能配置',
    text: '智能化配置系统，简化操作流程',
  },
];

// 创建请求处理程序
export const handlers = [
  // 用户登录
  http.post(`${API_BASE_URL}${UserApiEndpoints.SignIn}`, () => {
    return HttpResponse.json({
      status: 200,
      data: {
        token: 'mock-jwt-token',
        expiresIn: 3600,
        user: mockUsers[0],
      },
      message: '登录成功',
    });
  }),

  // 获取特性列表
  http.get(`${API_BASE_URL}${FeatureApiEndpoints.Features}`, () => {
    return HttpResponse.json({
      status: 200,
      data: mockFeatures,
      message: '获取成功',
    });
  }),

  // 获取产品列表
  http.get(`${API_BASE_URL}${ProductApiEndpoints.Products}`, () => {
    return HttpResponse.json({
      status: 200,
      data: mockProducts,
      message: '获取成功',
    });
  }),

  // 获取单个产品
  http.get(`${API_BASE_URL}${ProductApiEndpoints.ProductById.replace(':id', ':productId')}`, ({ params }) => {
    const { productId } = params;
    const product = mockProducts.find(p => p.id === Number(productId));

    if (product) {
      return HttpResponse.json({
        status: 200,
        data: product,
        message: '获取成功',
      });
    }

    return new HttpResponse(
      JSON.stringify({
        status: 404,
        message: '产品不存在',
      }),
      { status: 404 }
    );
  }),

  // 获取产品信息
  http.get(`${API_BASE_URL}${ProductApiEndpoints.ProductsInfo}`, () => {
    // 模拟从 API 获取的产品数据
    // 这里我们返回与静态数据相同的数据，但添加一个新产品来区分
    const extendedProducts = [
      ...mockProducts,
      {
        id: 10,
        title: 'API 返回的新产品',
        description: '这是一个从 API 返回的新产品，只有在使用 API 数据时才会显示。',
        features: [
          'API 独家特性',
          '实时数据更新',
          '动态内容'
        ],
        applications: [
          '在线展示',
          '数据分析',
          '实时监控'
        ],
        images: [
          { id: 1, src: '/img/api-product-1.jpg', alt: 'API 产品图片' }
        ],
        specifications: {
          '数据来源': 'API',
          '更新时间': '实时',
          '版本': '1.0.0'
        }
      }
    ];

    return HttpResponse.json(extendedProducts);
  }),
];

// 创建 MSW 服务工作者
export const worker = setupWorker(...handlers);

// 启动模拟服务
export const startMockService = () => {
  if (IS_DEV && USE_MOCK) {
    worker.start({
      onUnhandledRequest: 'bypass',
    }).catch(error => {
      console.error('MSW 启动失败:', error);
    });
    console.log('🔶 Mock Service Worker 已启动');
  }
};

export default startMockService;