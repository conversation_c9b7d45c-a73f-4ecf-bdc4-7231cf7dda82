import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { userStore } from '../../store/userStore';
import { toast } from 'sonner';
import env from '../../utils/env';
import { globalRateLimiter } from '../../utils/rateLimiter';

// API 响应类型
export interface ApiResponse<T = any> {
  status: number;
  data: T;
  message?: string;
}

// 重试配置
export interface RetryConfig {
  retries?: number;
  retryDelay?: number;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG = {
  retries: 1,
  retryDelay: 1000,
};

// 环境类型
export type Environment = 'development' | 'production' | 'test';

// 结果枚举
export enum ResultEnum {
  SUCCESS = 200,
  ERROR = 500,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
}

/**
 * API 客户端类
 * 统一处理请求、响应、错误处理和认证
 */
class ApiClient {
  private instance: AxiosInstance;
  private environment: Environment;

  constructor() {
    // 使用环境工具获取API基础URL
    const apiUrl = env.API_BASE_URL || 'http://localhost:3000';
    this.environment = env.NODE_ENV as Environment;

    this.instance = axios.create({
      baseURL: apiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Version': '1.0',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 检查速率限制
        const url = config.url || '';
        const method = config.method?.toUpperCase() || 'GET';
        const rateLimitResult = globalRateLimiter.isAllowed(url, method);

        if (!rateLimitResult.allowed) {
          const error = new Error(`Rate limit exceeded. Retry after ${rateLimitResult.retryAfter}ms`);
          error.name = 'RateLimitError';
          throw error;
        }

        const token = userStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 直接返回数据部分
        const { status, data, message } = response.data;

        if (status === ResultEnum.SUCCESS) {
          return data;
        }

        throw new Error(message || '发生未知错误');
      },
      (error: AxiosError<ApiResponse>) => {
        // 处理错误
        const { response, message } = error || {};
        const errMsg = response?.data?.message || message || '请求失败，请稍后重试';

        // 显示错误消息
        toast.error(errMsg, {
          position: 'top-center',
        });

        // 401 错误处理
        if (response?.status === ResultEnum.UNAUTHORIZED) {
          userStore.getState().actions.logout();
        }

        return Promise.reject(error);
      }
    );
  }

  // 通用请求方法
  public async request<T>(config: AxiosRequestConfig & RetryConfig): Promise<T> {
    const { retries = DEFAULT_RETRY_CONFIG.retries, retryDelay = DEFAULT_RETRY_CONFIG.retryDelay, ...axiosConfig } = config;

    let lastError: any;
    let attempt = 0;

    while (attempt < (retries || 0) + 1) {
      try {
        return await this.instance.request<any, T>(axiosConfig);
      } catch (error) {
        lastError = error;

        // 不重试 401, 403, 404 错误
        if (axios.isAxiosError(error) && error.response) {
          const status = error.response.status;
          if (status === ResultEnum.UNAUTHORIZED ||
              status === ResultEnum.FORBIDDEN ||
              status === ResultEnum.NOT_FOUND) {
            break;
          }
        }

        // 最后一次尝试失败，不再延迟
        if (attempt === retries) {
          break;
        }

        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        attempt++;
      }
    }

    throw lastError;
  }

  // 便捷方法
  public async get<T>(url: string, config?: AxiosRequestConfig & RetryConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig & RetryConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig & RetryConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  // PATCH请求
  public async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.patch<any, T>(url, data, config);
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig & RetryConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }



  /**
   * 发送 Beacon 请求（用于页面卸载前发送数据）
   * @param url 请求地址
   * @returns 完整的请求URL
   */
  public sendBeacon(url: string): string {
    const fullUrl = `${this.instance.defaults.baseURL}${url}`;
    console.log("full url", fullUrl);
    return fullUrl;
  }

  /**
   * 创建取消令牌
   * @param timeoutMs 超时时间（毫秒）
   * @returns AbortSignal 实例
   */
  public createAbortSignal(timeoutMs?: number): AbortSignal {
    if (timeoutMs && typeof AbortSignal.timeout === 'function') {
      return AbortSignal.timeout(timeoutMs);
    }

    const controller = new AbortController();
    if (timeoutMs) {
      setTimeout(() => controller.abort(), timeoutMs);
    }

    return controller.signal;
  }

  // 获取当前环境
  public getEnvironment(): Environment {
    return this.environment;
  }
}

// 创建单例实例
const apiClient = new ApiClient();

export default apiClient;