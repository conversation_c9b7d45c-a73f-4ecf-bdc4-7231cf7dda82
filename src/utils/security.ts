/**
 * 安全工具类
 * 提供输入验证、XSS防护、CSRF防护等安全功能
 */

import DOMPurify from 'dompurify';

/**
 * 输入验证规则
 */
export const ValidationRules = {
  // 邮箱验证 - 更严格的正则表达式
  email: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  
  // 用户名验证 - 只允许字母、数字、下划线
  username: /^[a-zA-Z0-9_]{3,20}$/,
  
  // 密码强度验证 - 至少8位，包含大小写字母、数字和特殊字符
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  
  // 电话号码验证
  phone: /^1[3-9]\d{9}$/,
  
  // URL验证
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // 防止SQL注入的基础检查
  sqlInjection: /('|(\\')|(;)|(\\;)|(\\|)|(\\|)|(\\)|(\\\\))/i,
  
  // 防止XSS的基础检查
  xss: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
};

/**
 * 输入验证器
 */
export class InputValidator {
  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): boolean {
    return ValidationRules.email.test(email.trim());
  }

  /**
   * 验证用户名
   */
  static validateUsername(username: string): boolean {
    return ValidationRules.username.test(username.trim());
  }

  /**
   * 验证密码强度
   */
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('密码长度至少8位');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 检查是否包含潜在的SQL注入
   */
  static checkSqlInjection(input: string): boolean {
    return ValidationRules.sqlInjection.test(input);
  }

  /**
   * 检查是否包含潜在的XSS攻击
   */
  static checkXss(input: string): boolean {
    return ValidationRules.xss.test(input);
  }

  /**
   * 综合安全检查
   */
  static securityCheck(input: string): {
    isSafe: boolean;
    threats: string[];
  } {
    const threats: string[] = [];
    
    if (this.checkSqlInjection(input)) {
      threats.push('潜在SQL注入风险');
    }
    
    if (this.checkXss(input)) {
      threats.push('潜在XSS攻击风险');
    }

    return {
      isSafe: threats.length === 0,
      threats
    };
  }
}

/**
 * XSS防护工具
 */
export class XSSProtection {
  /**
   * 清理HTML内容，防止XSS攻击
   */
  static sanitizeHtml(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
      ALLOWED_ATTR: ['href', 'title'],
      ALLOW_DATA_ATTR: false,
    });
  }

  /**
   * 转义HTML特殊字符
   */
  static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 清理用户输入
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除javascript协议
      .replace(/on\w+=/gi, ''); // 移除事件处理器
  }
}

/**
 * CSRF防护工具
 */
export class CSRFProtection {
  private static readonly TOKEN_KEY = 'csrf_token';
  
  /**
   * 生成CSRF令牌
   */
  static generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 设置CSRF令牌
   */
  static setToken(): string {
    const token = this.generateToken();
    sessionStorage.setItem(this.TOKEN_KEY, token);
    return token;
  }

  /**
   * 获取CSRF令牌
   */
  static getToken(): string | null {
    return sessionStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 验证CSRF令牌
   */
  static validateToken(token: string): boolean {
    const storedToken = this.getToken();
    return storedToken !== null && storedToken === token;
  }

  /**
   * 清除CSRF令牌
   */
  static clearToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY);
  }
}

/**
 * 安全的随机数生成器
 */
export class SecureRandom {
  /**
   * 生成安全的随机字符串
   */
  static generateSecureId(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 生成UUID v4
   */
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 生成安全的会话ID
   */
  static generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = this.generateSecureId(16);
    return `${timestamp}_${randomPart}`;
  }
}

/**
 * 内容安全策略(CSP)辅助工具
 */
export class CSPHelper {
  /**
   * 生成nonce值用于内联脚本
   */
  static generateNonce(): string {
    return SecureRandom.generateSecureId(16);
  }

  /**
   * 验证资源URL是否安全
   */
  static isUrlSafe(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // 只允许HTTPS和相对URL
      return urlObj.protocol === 'https:' || url.startsWith('/');
    } catch {
      return false;
    }
  }
}

/**
 * 敏感数据处理工具
 */
export class SensitiveDataHandler {
  /**
   * 脱敏手机号
   */
  static maskPhone(phone: string): string {
    if (phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  /**
   * 脱敏邮箱
   */
  static maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    if (!username || !domain) return email;
    
    const maskedUsername = username.length > 2 
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : username;
    
    return `${maskedUsername}@${domain}`;
  }

  /**
   * 脱敏身份证号
   */
  static maskIdCard(idCard: string): string {
    if (idCard.length !== 18) return idCard;
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  }
}

export default {
  InputValidator,
  XSSProtection,
  CSRFProtection,
  SecureRandom,
  CSPHelper,
  SensitiveDataHandler,
};
