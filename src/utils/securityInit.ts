/**
 * 安全初始化模块
 * 在应用启动时进行安全配置和检查
 */

import { ThirdPartyConfigManager, secureConfig } from './secureConfig';
import { CSRFProtection } from './security';
import { cspManager } from './cspConfig';
import { CSPViolationHandler, SecureStorage } from './securityEnhancements';
import { HijackingDetector } from './hijackingProtection';
import ConsoleManager from './consoleManager';

/**
 * 安全初始化类
 */
export class SecurityInitializer {
  private static initialized = false;

  /**
   * 初始化安全配置
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    console.log('🔒 初始化安全配置...');

    try {
      // 1. 验证环境配置
      this.validateEnvironment();

      // 2. 初始化CSRF保护
      this.initializeCSRFProtection();

      // 3. 配置第三方服务
      await this.configureThirdPartyServices();

      // 4. 设置安全头和CSP
      this.setupSecurityHeaders();

      // 5. 配置内容安全策略
      this.setupContentSecurityPolicy();

      // 6. 初始化Console管理
      this.initializeConsoleManager();

      // 7. 初始化CSP违规监听
      this.initializeCSPViolationHandler();

      // 8. 初始化存储清理
      this.initializeStorageCleanup();

      // 9. 初始化劫持防护
      this.initializeHijackingProtection();

      // 10. 初始化错误监控
      this.initializeErrorMonitoring();

      this.initialized = true;
      console.log('✅ 安全配置初始化完成');
    } catch (error) {
      console.error('❌ 安全配置初始化失败:', error);
      throw error;
    }
  }

  /**
   * 验证环境配置
   */
  private static validateEnvironment(): void {
    const validation = secureConfig.validateConfig();
    
    if (!validation.isValid) {
      console.warn('⚠️ 配置验证失败:', validation.errors);
      // 在开发环境中抛出错误，生产环境中记录警告
      if (secureConfig.get('NODE_ENV') === 'development') {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 检查必要的安全配置
    const requiredSecurityConfigs = [
      'API_BASE_URL',
      'NODE_ENV'
    ];

    const missingConfigs = requiredSecurityConfigs.filter(
      config => !secureConfig.get(config)
    );

    if (missingConfigs.length > 0) {
      throw new Error(`缺少必要的安全配置: ${missingConfigs.join(', ')}`);
    }
  }

  /**
   * 初始化CSRF保护
   */
  private static initializeCSRFProtection(): void {
    // 生成并设置CSRF令牌
    const token = CSRFProtection.setToken();
    console.log('🛡️ CSRF保护已启用');
    
    // 在开发环境中显示令牌（仅用于调试）
    if (secureConfig.get('NODE_ENV') === 'development') {
      console.log('🔑 CSRF Token:', token.substring(0, 8) + '...');
    }
  }

  /**
   * 配置第三方服务
   */
  private static async configureThirdPartyServices(): Promise<void> {
    // 配置EmailJS
    const emailJSConfig = {
      serviceId: secureConfig.get('VITE_EMAILJS_SERVICE_ID') || 'default_service',
      templateId: secureConfig.get('VITE_EMAILJS_TEMPLATE_ID') || 'default_template',
      publicKey: secureConfig.get('VITE_EMAILJS_PUBLIC_KEY') || 'default_key',
    };

    // 检查是否为默认值
    if (emailJSConfig.serviceId === 'default_service') {
      console.warn('⚠️ EmailJS配置使用默认值，请在生产环境中配置实际值');
    }

    ThirdPartyConfigManager.setEmailJSConfig(emailJSConfig);

    // 配置分析服务
    const analyticsConfig = {
      trackingId: secureConfig.get('VITE_ANALYTICS_TRACKING_ID') || 'default_tracking',
      apiEndpoint: secureConfig.get('VITE_ANALYTICS_API') || '/api/analytics',
    };

    ThirdPartyConfigManager.setAnalyticsConfig(analyticsConfig);

    console.log('🔧 第三方服务配置完成');
  }

  /**
   * 设置安全头
   */
  private static setupSecurityHeaders(): void {
    // 这些头部主要在nginx配置中设置，这里做客户端补充
    if (typeof document !== 'undefined') {
      // 设置CSP meta标签作为备用
      const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (!existingCSP) {
        const cspMeta = document.createElement('meta');
        cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
        cspMeta.setAttribute('content', 
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
          "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " +
          "img-src 'self' data: https:; " +
          "font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; " +
          "connect-src 'self' https://api.starrier.org; " +
          "frame-src 'none'; " +
          "object-src 'none';"
        );
        document.head.appendChild(cspMeta);
      }
    }

    console.log('🛡️ 安全头配置完成');
  }

  /**
   * 配置内容安全策略
   */
  private static setupContentSecurityPolicy(): void {
    const isDevelopment = secureConfig.get('NODE_ENV') === 'development';

    // 根据环境应用CSP配置
    cspManager.applyEnvironmentConfig(isDevelopment);

    // 在开发环境中显示nonce值
    if (isDevelopment) {
      console.log('🔑 CSP Nonce:', cspManager.getNonce().substring(0, 8) + '...');
    }

    console.log('🛡️ 内容安全策略配置完成');
  }

  /**
   * 初始化Console管理器
   */
  private static initializeConsoleManager(): void {
    ConsoleManager.initialize();
    console.log('📝 Console管理器已初始化');
  }

  /**
   * 初始化CSP违规监听
   */
  private static initializeCSPViolationHandler(): void {
    CSPViolationHandler.initialize();
    console.log('🚨 CSP违规监听已启用');
  }

  /**
   * 初始化存储清理
   */
  private static initializeStorageCleanup(): void {
    // 立即清理一次过期数据
    SecureStorage.cleanup();

    // 设置定期清理
    setInterval(() => {
      SecureStorage.cleanup();
    }, 60 * 60 * 1000); // 每小时清理一次

    console.log('🧹 存储清理机制已启用');
  }

  /**
   * 初始化劫持防护
   */
  private static initializeHijackingProtection(): void {
    HijackingDetector.getInstance().initialize();
    console.log('🛡️ 劫持防护系统已启用');
  }

  /**
   * 初始化错误监控
   */
  private static initializeErrorMonitoring(): void {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.handleGlobalError('JavaScript Error', event.error, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Promise拒绝处理
    window.addEventListener('unhandledrejection', (event) => {
      this.handleGlobalError('Unhandled Promise Rejection', event.reason, {
        promise: event.promise,
      });
    });

    console.log('📊 错误监控已启用');
  }

  /**
   * 处理全局错误
   */
  private static handleGlobalError(type: string, error: any, context: any): void {
    const errorInfo = {
      type,
      message: error?.message || String(error),
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    // 在开发环境中打印详细错误信息
    if (secureConfig.get('NODE_ENV') === 'development') {
      console.group(`🚨 ${type}`);
      console.error('Error:', error);
      console.log('Context:', context);
      console.groupEnd();
    }

    // 在生产环境中发送错误报告
    if (secureConfig.get('NODE_ENV') === 'production' && 
        secureConfig.get('VITE_ENABLE_ERROR_REPORTING')) {
      this.sendErrorReport(errorInfo);
    }
  }

  /**
   * 发送错误报告
   */
  private static async sendErrorReport(errorInfo: any): Promise<void> {
    try {
      // 这里可以集成错误报告服务
      // 例如：Sentry, LogRocket, 或自定义错误收集API
      
      const response = await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorInfo),
      });

      if (!response.ok) {
        throw new Error(`Error reporting failed: ${response.status}`);
      }
    } catch (error) {
      // 错误报告失败时不应该影响应用运行
      console.warn('Failed to send error report:', error);
    }
  }

  /**
   * 获取安全状态
   */
  static getSecurityStatus(): {
    initialized: boolean;
    csrfToken: string | null;
    environment: string;
    securityScore: number;
  } {
    return {
      initialized: this.initialized,
      csrfToken: CSRFProtection.getToken(),
      environment: secureConfig.get('NODE_ENV'),
      securityScore: this.calculateSecurityScore(),
    };
  }

  /**
   * 计算安全分数
   */
  private static calculateSecurityScore(): number {
    let score = 0;
    const checks = [
      () => this.initialized, // 基础初始化
      () => CSRFProtection.getToken() !== null, // CSRF保护
      () => secureConfig.get('NODE_ENV') === 'production', // 生产环境
      () => secureConfig.get('VITE_ENABLE_ERROR_REPORTING'), // 错误报告
      () => ThirdPartyConfigManager.getEmailJSConfig() !== null, // 第三方配置
    ];

    checks.forEach(check => {
      if (check()) score += 20;
    });

    return score;
  }

  /**
   * 重置安全配置
   */
  static reset(): void {
    this.initialized = false;
    CSRFProtection.clearToken();
    ThirdPartyConfigManager.clearAllConfigs();
    console.log('🔄 安全配置已重置');
  }
}

/**
 * 导出初始化函数
 */
export const initializeSecurity = SecurityInitializer.initialize.bind(SecurityInitializer);

export default SecurityInitializer;
