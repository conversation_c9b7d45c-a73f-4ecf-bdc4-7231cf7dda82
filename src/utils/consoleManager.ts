/**
 * Console管理器
 * 在生产环境中禁用或重定向console输出，防止信息泄露
 */

interface ConsoleMethod {
  log: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  debug: (...args: any[]) => void;
  info: (...args: any[]) => void;
  trace: (...args: any[]) => void;
  table: (...args: any[]) => void;
  group: (...args: any[]) => void;
  groupEnd: () => void;
  time: (label?: string) => void;
  timeEnd: (label?: string) => void;
}

/**
 * Console安全管理器
 */
export class ConsoleManager {
  private static originalConsole: ConsoleMethod;
  private static isProduction = process.env.NODE_ENV === 'production';
  private static logBuffer: Array<{ level: string; args: any[]; timestamp: number }> = [];
  private static maxBufferSize = 100;

  /**
   * 初始化Console管理器
   */
  static initialize(): void {
    // 保存原始console方法
    this.originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      debug: console.debug.bind(console),
      info: console.info.bind(console),
      trace: console.trace.bind(console),
      table: console.table.bind(console),
      group: console.group.bind(console),
      groupEnd: console.groupEnd.bind(console),
      time: console.time.bind(console),
      timeEnd: console.timeEnd.bind(console),
    };

    if (this.isProduction) {
      this.setupProductionConsole();
    } else {
      this.setupDevelopmentConsole();
    }
  }

  /**
   * 设置生产环境Console
   */
  private static setupProductionConsole(): void {
    // 在生产环境中禁用大部分console方法
    console.log = this.createSafeLogger('log');
    console.debug = this.createSafeLogger('debug');
    console.info = this.createSafeLogger('info');
    console.trace = this.createSafeLogger('trace');
    console.table = this.createSafeLogger('table');
    console.group = this.createSafeLogger('group');
    console.groupEnd = this.createSafeLogger('groupEnd');
    console.time = this.createSafeLogger('time');
    console.timeEnd = this.createSafeLogger('timeEnd');

    // 保留warn和error，但进行过滤
    console.warn = this.createFilteredLogger('warn');
    console.error = this.createFilteredLogger('error');
  }

  /**
   * 设置开发环境Console
   */
  private static setupDevelopmentConsole(): void {
    // 开发环境保持原有功能，但添加额外的安全检查
    console.log = this.createSecureLogger('log');
    console.warn = this.createSecureLogger('warn');
    console.error = this.createSecureLogger('error');
    console.debug = this.createSecureLogger('debug');
    console.info = this.createSecureLogger('info');
  }

  /**
   * 创建安全的日志记录器（生产环境用）
   */
  private static createSafeLogger(level: keyof ConsoleMethod): (...args: any[]) => void {
    return (...args: any[]) => {
      // 生产环境中静默处理，只记录到内部缓冲区
      this.addToBuffer(level, args);
    };
  }

  /**
   * 创建过滤的日志记录器（生产环境的warn和error）
   */
  private static createFilteredLogger(level: 'warn' | 'error'): (...args: any[]) => void {
    return (...args: any[]) => {
      const filteredArgs = this.filterSensitiveData(args);
      this.addToBuffer(level, filteredArgs);
      
      // 只有非敏感的错误和警告才输出到控制台
      if (this.shouldLog(filteredArgs)) {
        this.originalConsole[level](...filteredArgs);
      }
    };
  }

  /**
   * 创建安全的日志记录器（开发环境用）
   */
  private static createSecureLogger(level: keyof ConsoleMethod): (...args: any[]) => void {
    return (...args: any[]) => {
      const filteredArgs = this.filterSensitiveData(args);
      this.addToBuffer(level, filteredArgs);
      this.originalConsole[level](...filteredArgs);
    };
  }

  /**
   * 过滤敏感数据
   */
  private static filterSensitiveData(args: any[]): any[] {
    const sensitivePatterns = [
      /password/i,
      /token/i,
      /secret/i,
      /key/i,
      /auth/i,
      /credential/i,
    ];

    return args.map(arg => {
      if (typeof arg === 'string') {
        // 检查字符串是否包含敏感信息
        if (sensitivePatterns.some(pattern => pattern.test(arg))) {
          return '[SENSITIVE DATA FILTERED]';
        }
        return arg;
      }

      if (typeof arg === 'object' && arg !== null) {
        const filtered: any = {};
        for (const [key, value] of Object.entries(arg)) {
          if (sensitivePatterns.some(pattern => pattern.test(key))) {
            filtered[key] = '[FILTERED]';
          } else if (typeof value === 'string' && 
                     sensitivePatterns.some(pattern => pattern.test(value))) {
            filtered[key] = '[FILTERED]';
          } else {
            filtered[key] = value;
          }
        }
        return filtered;
      }

      return arg;
    });
  }

  /**
   * 判断是否应该输出日志
   */
  private static shouldLog(args: any[]): boolean {
    // 检查是否包含被过滤的内容
    const hasFiltered = args.some(arg => 
      typeof arg === 'string' && arg.includes('[SENSITIVE DATA FILTERED]')
    );
    
    return !hasFiltered;
  }

  /**
   * 添加到内部缓冲区
   */
  private static addToBuffer(level: string, args: any[]): void {
    this.logBuffer.push({
      level,
      args,
      timestamp: Date.now(),
    });

    // 保持缓冲区大小
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.shift();
    }
  }

  /**
   * 获取日志缓冲区（用于调试或错误报告）
   */
  static getLogBuffer(): Array<{ level: string; args: any[]; timestamp: number }> {
    return [...this.logBuffer];
  }

  /**
   * 清空日志缓冲区
   */
  static clearLogBuffer(): void {
    this.logBuffer = [];
  }

  /**
   * 恢复原始console（用于调试）
   */
  static restoreOriginalConsole(): void {
    if (this.originalConsole) {
      Object.assign(console, this.originalConsole);
    }
  }

  /**
   * 创建安全的错误报告
   */
  static createErrorReport(): any {
    const recentLogs = this.logBuffer
      .filter(log => log.level === 'error' || log.level === 'warn')
      .slice(-10); // 只取最近10条

    return {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      recentLogs: recentLogs.map(log => ({
        level: log.level,
        message: log.args.join(' '),
        timestamp: log.timestamp,
      })),
    };
  }

  /**
   * 发送错误报告到服务器
   */
  static async sendErrorReport(): Promise<void> {
    if (!this.isProduction) return;

    try {
      const report = this.createErrorReport();
      await fetch('/api/error-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report),
      });
    } catch (error) {
      // 静默处理发送失败
    }
  }

  /**
   * 临时启用console（用于调试）
   */
  static enableTemporaryLogging(durationMs: number = 30000): void {
    if (!this.isProduction) return;

    console.log = this.originalConsole.log;
    console.warn = this.originalConsole.warn;
    console.error = this.originalConsole.error;

    setTimeout(() => {
      this.setupProductionConsole();
    }, durationMs);
  }
}

/**
 * 自动初始化Console管理器
 */
if (typeof window !== 'undefined') {
  // 在DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      ConsoleManager.initialize();
    });
  } else {
    ConsoleManager.initialize();
  }
}

export default ConsoleManager;
