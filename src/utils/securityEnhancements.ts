/**
 * 安全增强工具
 * 提供额外的安全防护功能
 */

/**
 * 安全的本地存储管理器
 */
export class SecureStorage {
  private static readonly ENCRYPTION_KEY = 'app_secure_key';
  private static readonly EXPIRY_SUFFIX = '_expiry';

  /**
   * 简单的数据混淆（客户端加密有限，主要防止明文存储）
   */
  private static obfuscate(data: string): string {
    const key = this.ENCRYPTION_KEY;
    let result = '';
    
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    
    return btoa(result);
  }

  /**
   * 解混淆数据
   */
  private static deobfuscate(obfuscatedData: string): string {
    try {
      const decoded = atob(obfuscatedData);
      const key = this.ENCRYPTION_KEY;
      let result = '';
      
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
        result += String.fromCharCode(charCode);
      }
      
      return result;
    } catch {
      return '';
    }
  }

  /**
   * 安全设置数据到localStorage
   */
  static setItem(key: string, value: any, expiryMinutes?: number): boolean {
    try {
      const data = {
        value,
        timestamp: Date.now(),
        expiry: expiryMinutes ? Date.now() + (expiryMinutes * 60 * 1000) : null,
      };
      
      const obfuscatedData = this.obfuscate(JSON.stringify(data));
      localStorage.setItem(key, obfuscatedData);
      
      return true;
    } catch (error) {
      console.error('SecureStorage.setItem failed:', error);
      return false;
    }
  }

  /**
   * 安全获取localStorage数据
   */
  static getItem<T = any>(key: string): T | null {
    try {
      const obfuscatedData = localStorage.getItem(key);
      if (!obfuscatedData) return null;
      
      const deobfuscatedData = this.deobfuscate(obfuscatedData);
      if (!deobfuscatedData) return null;
      
      const data = JSON.parse(deobfuscatedData);
      
      // 检查是否过期
      if (data.expiry && Date.now() > data.expiry) {
        this.removeItem(key);
        return null;
      }
      
      return data.value;
    } catch (error) {
      console.error('SecureStorage.getItem failed:', error);
      return null;
    }
  }

  /**
   * 移除localStorage数据
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('SecureStorage.removeItem failed:', error);
    }
  }

  /**
   * 清理过期数据
   */
  static cleanup(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        this.getItem(key); // 这会自动清理过期数据
      });
    } catch (error) {
      console.error('SecureStorage.cleanup failed:', error);
    }
  }
}

/**
 * 安全的第三方脚本加载器
 */
export class SecureScriptLoader {
  private static loadedScripts = new Set<string>();

  /**
   * 安全加载第三方脚本
   */
  static async loadScript(src: string, options: {
    integrity?: string;
    crossorigin?: string;
    timeout?: number;
    retries?: number;
  } = {}): Promise<void> {
    const {
      integrity,
      crossorigin = 'anonymous',
      timeout = 10000,
      retries = 2
    } = options;

    // 避免重复加载
    if (this.loadedScripts.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      let attempts = 0;
      
      const loadAttempt = () => {
        attempts++;
        
        const script = document.createElement('script');
        script.src = src;
        script.crossOrigin = crossorigin;
        
        if (integrity) {
          script.integrity = integrity;
        }

        // 设置超时
        const timeoutId = setTimeout(() => {
          script.remove();
          if (attempts <= retries) {
            console.warn(`Script load timeout, retrying (${attempts}/${retries}): ${src}`);
            loadAttempt();
          } else {
            reject(new Error(`Script load timeout after ${retries} retries: ${src}`));
          }
        }, timeout);

        script.onload = () => {
          clearTimeout(timeoutId);
          this.loadedScripts.add(src);
          resolve();
        };

        script.onerror = () => {
          clearTimeout(timeoutId);
          script.remove();
          if (attempts <= retries) {
            console.warn(`Script load error, retrying (${attempts}/${retries}): ${src}`);
            setTimeout(loadAttempt, 1000 * attempts); // 递增延迟
          } else {
            reject(new Error(`Script load failed after ${retries} retries: ${src}`));
          }
        };

        document.head.appendChild(script);
      };

      loadAttempt();
    });
  }
}

/**
 * 安全的DOM操作工具
 */
export class SecureDOM {
  /**
   * 安全的HTML转义
   */
  static escapeHtml(text: string): string {
    const textNode = document.createTextNode(text);
    const div = document.createElement('div');
    div.appendChild(textNode);
    return div.innerHTML;
  }

  /**
   * 安全的属性设置
   */
  static setSecureAttribute(element: Element, name: string, value: string): void {
    // 防止设置危险属性
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus'];
    if (dangerousAttrs.includes(name.toLowerCase())) {
      console.warn(`Blocked dangerous attribute: ${name}`);
      return;
    }

    // 防止javascript:协议
    if (name.toLowerCase() === 'href' && value.toLowerCase().startsWith('javascript:')) {
      console.warn(`Blocked javascript: protocol in href`);
      return;
    }

    element.setAttribute(name, value);
  }

  /**
   * 安全的事件监听器添加
   */
  static addSecureEventListener(
    element: Element,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): () => void {
    // 验证事件类型
    const allowedEvents = [
      'click', 'submit', 'change', 'input', 'focus', 'blur',
      'mouseenter', 'mouseleave', 'keydown', 'keyup'
    ];
    
    if (!allowedEvents.includes(event)) {
      console.warn(`Potentially unsafe event type: ${event}`);
    }

    element.addEventListener(event, handler, options);
    
    // 返回清理函数
    return () => {
      element.removeEventListener(event, handler, options);
    };
  }
}

/**
 * 安全的错误处理器
 */
export class SecureErrorHandler {
  private static sensitivePatterns = [
    /password/i,
    /token/i,
    /secret/i,
    /key/i,
    /auth/i,
  ];

  /**
   * 清理错误信息中的敏感数据
   */
  static sanitizeError(error: any): any {
    if (typeof error === 'string') {
      return this.sanitizeString(error);
    }

    if (error instanceof Error) {
      return {
        name: error.name,
        message: this.sanitizeString(error.message),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      };
    }

    if (typeof error === 'object' && error !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(error)) {
        if (this.isSensitiveKey(key)) {
          sanitized[key] = '[REDACTED]';
        } else if (typeof value === 'string') {
          sanitized[key] = this.sanitizeString(value);
        } else {
          sanitized[key] = value;
        }
      }
      return sanitized;
    }

    return error;
  }

  /**
   * 清理字符串中的敏感信息
   */
  private static sanitizeString(str: string): string {
    let sanitized = str;
    
    // 移除可能的密码、令牌等
    this.sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    // 移除可能的URL中的敏感参数
    sanitized = sanitized.replace(/([?&])(token|key|password|secret)=[^&\s]*/gi, '$1$2=[REDACTED]');

    return sanitized;
  }

  /**
   * 检查是否为敏感键名
   */
  private static isSensitiveKey(key: string): boolean {
    return this.sensitivePatterns.some(pattern => pattern.test(key));
  }
}

/**
 * 内容安全策略违规报告处理器
 */
export class CSPViolationHandler {
  /**
   * 初始化CSP违规监听
   */
  static initialize(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      this.handleViolation(event);
    });
  }

  /**
   * 处理CSP违规事件
   */
  private static handleViolation(event: SecurityPolicyViolationEvent): void {
    const violation = {
      blockedURI: event.blockedURI,
      violatedDirective: event.violatedDirective,
      originalPolicy: event.originalPolicy,
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
      columnNumber: event.columnNumber,
      timestamp: Date.now(),
    };

    // 在开发环境中记录详细信息
    if (process.env.NODE_ENV === 'development') {
      console.warn('CSP Violation:', violation);
    }

    // 在生产环境中发送到监控服务
    if (process.env.NODE_ENV === 'production') {
      this.reportViolation(violation);
    }
  }

  /**
   * 报告CSP违规到监控服务
   */
  private static async reportViolation(violation: any): Promise<void> {
    try {
      await fetch('/api/security/csp-violation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(violation),
      });
    } catch (error) {
      console.error('Failed to report CSP violation:', error);
    }
  }
}

export default {
  SecureStorage,
  SecureScriptLoader,
  SecureDOM,
  SecureErrorHandler,
  CSPViolationHandler,
};
