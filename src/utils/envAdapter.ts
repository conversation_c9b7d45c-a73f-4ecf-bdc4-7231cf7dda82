/**
 * 环境变量适配器
 * 为Next.js环境提供Vite风格的环境变量访问
 * 保持现有组件完全不变
 */

// 创建一个模拟的import.meta.env对象
const createImportMetaEnv = () => {
  const env: Record<string, string | undefined> = {};
  
  // 将Next.js的process.env映射到Vite的import.meta.env格式
  if (typeof process !== 'undefined' && process.env) {
    // 映射VITE_前缀的环境变量
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('VITE_')) {
        env[key] = process.env[key];
      }
      if (key.startsWith('NEXT_PUBLIC_')) {
        // 将NEXT_PUBLIC_映射为VITE_格式
        const viteKey = key.replace('NEXT_PUBLIC_', 'VITE_');
        env[viteKey] = process.env[key];
      }
    });
  }
  
  // 设置默认值，保持与原有配置一致
  env.VITE_API_BASE_URL = env.VITE_API_BASE_URL || '/api';
  env.VITE_API_PROXY_TARGET = env.VITE_API_PROXY_TARGET || 'https://api.starrier.org';
  env.VITE_USE_MOCK = env.VITE_USE_MOCK || 'false';
  env.VITE_ENABLE_ANALYTICS = env.VITE_ENABLE_ANALYTICS || 'true';
  env.VITE_ENABLE_ERROR_REPORTING = env.VITE_ENABLE_ERROR_REPORTING || 'false';
  
  return env;
};

// 创建模拟的import.meta对象
const createImportMeta = () => {
  return {
    env: createImportMetaEnv(),
    url: typeof window !== 'undefined' ? window.location.href : 'http://localhost:3012',
    hot: null, // Next.js不使用Vite的HMR
  };
};

// 在全局范围内设置import.meta（仅在需要时）
if (typeof globalThis !== 'undefined' && !globalThis.import) {
  (globalThis as any).import = {
    meta: createImportMeta()
  };
}

// 导出环境变量访问函数
export const getEnv = (key: string): string | undefined => {
  const env = createImportMetaEnv();
  return env[key];
};

// 导出完整的环境对象
export const env = createImportMetaEnv();

// 为了兼容性，也导出import.meta风格的访问
export const importMeta = createImportMeta();

export default {
  getEnv,
  env,
  importMeta,
};
