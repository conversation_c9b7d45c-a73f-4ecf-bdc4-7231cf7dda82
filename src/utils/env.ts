/**
 * 环境变量访问工具
 * 优先使用运行时环境变量，如果不存在则使用构建时环境变量
 */

// 定义环境变量类型
interface EnvConfig {
  API_BASE_URL: string;
  APP_BASE_PATH: string;
  NODE_ENV: string;
  [key: string]: string;
}

// 获取运行时环境变量
const getRuntimeEnv = (): Partial<EnvConfig> => {
  if (typeof window !== 'undefined' && window.ENV_CONFIG) {
    return window.ENV_CONFIG;
  }
  return {};
};

// 获取构建时环境变量
const getBuildTimeEnv = (): Partial<EnvConfig> => {
  // 兼容Vite和Next.js的环境变量访问方式
  const getEnvVar = (key: string): string | undefined => {
    // Next.js环境变量
    if (typeof process !== 'undefined' && process.env) {
      return process.env[key];
    }

    // Vite环境变量（仅在客户端可用）
    if (typeof window !== 'undefined' && import.meta?.env) {
      return import.meta.env[key];
    }

    return undefined;
  };

  return {
    API_BASE_URL: getEnvVar('VITE_API_BASE_URL') || getEnvVar('NEXT_PUBLIC_API_BASE_URL'),
    APP_BASE_PATH: getEnvVar('VITE_APP_BASE_PATH') || getEnvVar('NEXT_PUBLIC_APP_BASE_PATH'),
    NODE_ENV: getEnvVar('NODE_ENV') || getEnvVar('MODE'),
  };
};

// 合并环境变量，运行时环境变量优先
const env: EnvConfig = {
  ...getBuildTimeEnv(),
  ...getRuntimeEnv(),
  // 设置默认值
  API_BASE_URL: getRuntimeEnv().API_BASE_URL || getBuildTimeEnv().API_BASE_URL || '/api',
  APP_BASE_PATH: getRuntimeEnv().APP_BASE_PATH || getBuildTimeEnv().APP_BASE_PATH || '/',
  NODE_ENV: getRuntimeEnv().NODE_ENV || getBuildTimeEnv().NODE_ENV || 'production',
};

// 判断是否是生产环境
export const isProd = env.NODE_ENV === 'production';
// 判断是否是开发环境
export const isDev = env.NODE_ENV === 'development';
// 判断是否是测试环境
export const isTest = env.NODE_ENV === 'test';

// 导出环境变量
export default env;
