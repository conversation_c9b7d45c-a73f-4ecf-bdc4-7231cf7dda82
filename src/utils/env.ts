/**
 * 环境变量访问工具
 * 使用新的兼容工具，避免直接访问import.meta
 */

import { envConfig, isDevelopment, isProduction } from './envCompat';

// 定义环境变量类型
interface EnvConfig {
  API_BASE_URL: string;
  APP_BASE_PATH: string;
  NODE_ENV: string;
  [key: string]: string;
}

// 创建兼容的环境变量对象
const env: EnvConfig = {
  API_BASE_URL: envConfig.API_BASE_URL,
  APP_BASE_PATH: envConfig.APP_BASE_PATH,
  NODE_ENV: envConfig.NODE_ENV,
};

// 判断是否是生产环境
export const isProd = isProduction();
// 判断是否是开发环境
export const isDev = isDevelopment();
// 判断是否是测试环境
export const isTest = envConfig.NODE_ENV === 'test';

// 导出环境变量
export default env;
