/**
 * 客户端API速率限制器
 * 防止API请求过于频繁，提高安全性和用户体验
 */

interface RateLimitConfig {
  /** 时间窗口（毫秒） */
  windowMs: number;
  /** 最大请求次数 */
  maxRequests: number;
  /** 限制键生成函数 */
  keyGenerator?: (url: string, method: string) => string;
  /** 超出限制时的回调 */
  onLimitReached?: (key: string, retryAfter: number) => void;
}

interface RequestRecord {
  count: number;
  resetTime: number;
}

/**
 * 客户端速率限制器
 */
export class ClientRateLimiter {
  private requests = new Map<string, RequestRecord>();
  private config: Required<RateLimitConfig>;

  constructor(config: RateLimitConfig) {
    this.config = {
      windowMs: config.windowMs,
      maxRequests: config.maxRequests,
      keyGenerator: config.keyGenerator || this.defaultKeyGenerator,
      onLimitReached: config.onLimitReached || this.defaultOnLimitReached,
    };

    // 定期清理过期记录
    setInterval(() => this.cleanup(), this.config.windowMs);
  }

  /**
   * 默认键生成器
   */
  private defaultKeyGenerator(url: string, method: string): string {
    return `${method}:${url}`;
  }

  /**
   * 默认限制回调
   */
  private defaultOnLimitReached(key: string, retryAfter: number): void {
    console.warn(`Rate limit exceeded for ${key}. Retry after ${retryAfter}ms`);
  }

  /**
   * 检查是否允许请求
   */
  public isAllowed(url: string, method: string = 'GET'): {
    allowed: boolean;
    retryAfter?: number;
    remaining?: number;
  } {
    const key = this.config.keyGenerator(url, method);
    const now = Date.now();
    const record = this.requests.get(key);

    // 如果没有记录或已过期，创建新记录
    if (!record || now >= record.resetTime) {
      this.requests.set(key, {
        count: 1,
        resetTime: now + this.config.windowMs,
      });
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
      };
    }

    // 检查是否超出限制
    if (record.count >= this.config.maxRequests) {
      const retryAfter = record.resetTime - now;
      this.config.onLimitReached(key, retryAfter);
      return {
        allowed: false,
        retryAfter,
      };
    }

    // 增加计数
    record.count++;
    this.requests.set(key, record);

    return {
      allowed: true,
      remaining: this.config.maxRequests - record.count,
    };
  }

  /**
   * 清理过期记录
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now >= record.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * 重置特定键的限制
   */
  public reset(url: string, method: string = 'GET'): void {
    const key = this.config.keyGenerator(url, method);
    this.requests.delete(key);
  }

  /**
   * 重置所有限制
   */
  public resetAll(): void {
    this.requests.clear();
  }

  /**
   * 获取当前状态
   */
  public getStatus(url: string, method: string = 'GET'): {
    count: number;
    remaining: number;
    resetTime: number;
  } {
    const key = this.config.keyGenerator(url, method);
    const record = this.requests.get(key);
    const now = Date.now();

    if (!record || now >= record.resetTime) {
      return {
        count: 0,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs,
      };
    }

    return {
      count: record.count,
      remaining: Math.max(0, this.config.maxRequests - record.count),
      resetTime: record.resetTime,
    };
  }
}

/**
 * 预定义的速率限制器配置
 */
export const RateLimitPresets = {
  /** 严格限制：每分钟10次请求 */
  STRICT: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 10,
  },
  
  /** 中等限制：每分钟30次请求 */
  MODERATE: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 30,
  },
  
  /** 宽松限制：每分钟100次请求 */
  LENIENT: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 100,
  },
  
  /** 表单提交：每5分钟3次请求 */
  FORM_SUBMISSION: {
    windowMs: 5 * 60 * 1000, // 5分钟
    maxRequests: 3,
  },
  
  /** 登录尝试：每15分钟5次请求 */
  LOGIN_ATTEMPTS: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 5,
  },
};

/**
 * 全局速率限制器实例
 */
export const globalRateLimiter = new ClientRateLimiter(RateLimitPresets.MODERATE);

/**
 * 表单提交速率限制器
 */
export const formRateLimiter = new ClientRateLimiter({
  ...RateLimitPresets.FORM_SUBMISSION,
  onLimitReached: (key, retryAfter) => {
    const minutes = Math.ceil(retryAfter / (60 * 1000));
    alert(`提交过于频繁，请等待 ${minutes} 分钟后再试`);
  },
});

/**
 * 登录尝试速率限制器
 */
export const loginRateLimiter = new ClientRateLimiter({
  ...RateLimitPresets.LOGIN_ATTEMPTS,
  keyGenerator: (url, method) => {
    // 基于IP或用户标识生成键（这里简化为固定键）
    return `login_attempts`;
  },
  onLimitReached: (key, retryAfter) => {
    const minutes = Math.ceil(retryAfter / (60 * 1000));
    alert(`登录尝试过多，请等待 ${minutes} 分钟后再试`);
  },
});

/**
 * 速率限制装饰器
 * 用于装饰API调用函数
 */
export function withRateLimit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  limiter: ClientRateLimiter,
  url: string,
  method: string = 'GET'
): T {
  return (async (...args: Parameters<T>) => {
    const result = limiter.isAllowed(url, method);
    
    if (!result.allowed) {
      throw new Error(`Rate limit exceeded. Retry after ${result.retryAfter}ms`);
    }
    
    return fn(...args);
  }) as T;
}

/**
 * 创建带速率限制的fetch函数
 */
export function createRateLimitedFetch(limiter: ClientRateLimiter) {
  return async (url: string, options?: RequestInit): Promise<Response> => {
    const method = options?.method || 'GET';
    const result = limiter.isAllowed(url, method);
    
    if (!result.allowed) {
      throw new Error(`Rate limit exceeded. Retry after ${result.retryAfter}ms`);
    }
    
    return fetch(url, options);
  };
}

export default {
  ClientRateLimiter,
  RateLimitPresets,
  globalRateLimiter,
  formRateLimiter,
  loginRateLimiter,
  withRateLimit,
  createRateLimitedFetch,
};
