/**
 * 网页劫持防护系统
 * 提供全面的劫持检测和防护功能
 */

/**
 * 劫持检测器
 */
export class HijackingDetector {
  private static instance: HijackingDetector;
  private isInitialized = false;
  private originalLocation: string;
  private frameCheckInterval: number | null = null;
  private listeners: Array<() => void> = [];

  private constructor() {
    this.originalLocation = window.location.href;
  }

  static getInstance(): HijackingDetector {
    if (!HijackingDetector.instance) {
      HijackingDetector.instance = new HijackingDetector();
    }
    return HijackingDetector.instance;
  }

  /**
   * 初始化劫持检测
   */
  initialize(): void {
    if (this.isInitialized) return;

    this.detectClickjacking();
    this.detectFrameBreaking();
    this.detectURLHijacking();
    this.detectPostMessageAttacks();
    this.setupDOMIntegrityCheck();
    this.setupNavigationProtection();

    this.isInitialized = true;
    console.log('🛡️ 劫持防护系统已启动');
  }

  /**
   * 检测点击劫持攻击
   */
  private detectClickjacking(): void {
    // 检查是否在iframe中
    if (window.self !== window.top) {
      console.warn('🚨 检测到页面被嵌入iframe中');
      
      // 尝试跳出iframe
      try {
        if (window.top) {
          window.top.location.href = window.location.href;
        }
      } catch (error) {
        // 如果无法跳出，显示警告
        this.showHijackingWarning('点击劫持', '页面被恶意嵌入，请直接访问官方网站');
      }
    }

    // 监听iframe嵌入尝试
    this.frameCheckInterval = window.setInterval(() => {
      if (window.self !== window.top) {
        this.handleFrameDetection();
      }
    }, 1000);
  }

  /**
   * 检测框架破坏攻击
   */
  private detectFrameBreaking(): void {
    // 监听窗口大小变化（可能的iframe调整）
    const resizeHandler = () => {
      if (window.self !== window.top) {
        console.warn('🚨 检测到可能的框架操作');
      }
    };

    window.addEventListener('resize', resizeHandler);
    this.listeners.push(() => window.removeEventListener('resize', resizeHandler));
  }

  /**
   * 检测URL劫持
   */
  private detectURLHijacking(): void {
    // 监听URL变化
    const checkURL = () => {
      const currentURL = window.location.href;
      
      // 检查是否有可疑的URL参数
      const suspiciousParams = ['redirect', 'return_url', 'callback', 'next'];
      const urlParams = new URLSearchParams(window.location.search);
      
      suspiciousParams.forEach(param => {
        const value = urlParams.get(param);
        if (value && !this.isValidRedirectURL(value)) {
          console.warn(`🚨 检测到可疑的重定向参数: ${param}=${value}`);
        }
      });

      // 检查域名是否正确
      if (!this.isValidDomain(window.location.hostname)) {
        this.showHijackingWarning('域名劫持', '当前域名可能不是官方域名');
      }
    };

    // 立即检查
    checkURL();

    // 监听URL变化
    window.addEventListener('popstate', checkURL);
    this.listeners.push(() => window.removeEventListener('popstate', checkURL));
  }

  /**
   * 检测PostMessage攻击
   */
  private detectPostMessageAttacks(): void {
    const messageHandler = (event: MessageEvent) => {
      // 验证消息来源
      if (!this.isValidOrigin(event.origin)) {
        console.warn(`🚨 收到来自可疑源的消息: ${event.origin}`);
        return;
      }

      // 验证消息内容
      if (this.isSuspiciousMessage(event.data)) {
        console.warn('🚨 收到可疑的PostMessage内容');
        return;
      }
    };

    window.addEventListener('message', messageHandler);
    this.listeners.push(() => window.removeEventListener('message', messageHandler));
  }

  /**
   * 设置DOM完整性检查
   */
  private setupDOMIntegrityCheck(): void {
    // 检查关键元素是否被篡改
    const checkDOMIntegrity = () => {
      // 检查是否有可疑的iframe被注入
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        const src = iframe.getAttribute('src');
        if (src && !this.isValidIframeSrc(src)) {
          console.warn(`🚨 检测到可疑的iframe: ${src}`);
          iframe.remove();
        }
      });

      // 检查是否有可疑的脚本被注入
      const scripts = document.querySelectorAll('script[src]');
      scripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src && !this.isValidScriptSrc(src)) {
          console.warn(`🚨 检测到可疑的脚本: ${src}`);
          script.remove();
        }
      });
    };

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.tagName === 'IFRAME' || element.tagName === 'SCRIPT') {
                checkDOMIntegrity();
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.listeners.push(() => observer.disconnect());
  }

  /**
   * 设置导航保护
   */
  private setupNavigationProtection(): void {
    // 拦截可疑的导航
    const beforeUnloadHandler = (event: BeforeUnloadEvent) => {
      const currentURL = window.location.href;
      if (this.isSuspiciousNavigation(currentURL)) {
        event.preventDefault();
        event.returnValue = '检测到可疑的页面跳转，确定要离开吗？';
        return event.returnValue;
      }
    };

    window.addEventListener('beforeunload', beforeUnloadHandler);
    this.listeners.push(() => window.removeEventListener('beforeunload', beforeUnloadHandler));
  }

  /**
   * 处理框架检测
   */
  private handleFrameDetection(): void {
    // 尝试多种方法跳出框架
    const breakoutMethods = [
      () => { if (window.top) window.top.location.href = window.location.href; },
      () => { window.parent.location.href = window.location.href; },
      () => { document.body.style.display = 'none'; },
    ];

    breakoutMethods.forEach((method, index) => {
      try {
        method();
      } catch (error) {
        console.warn(`框架跳出方法 ${index + 1} 失败:`, error);
      }
    });
  }

  /**
   * 显示劫持警告
   */
  private showHijackingWarning(type: string, message: string): void {
    // 创建警告覆盖层
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 0, 0, 0.9);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 999999;
      font-family: Arial, sans-serif;
      font-size: 18px;
      text-align: center;
    `;

    overlay.innerHTML = `
      <h2>🚨 安全警告：${type}</h2>
      <p>${message}</p>
      <p>请关闭此页面并直接访问官方网站</p>
      <button onclick="window.close()" style="
        padding: 10px 20px;
        font-size: 16px;
        background: white;
        color: red;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 20px;
      ">关闭页面</button>
    `;

    document.body.appendChild(overlay);
  }

  /**
   * 验证重定向URL是否安全
   */
  private isValidRedirectURL(url: string): boolean {
    try {
      const urlObj = new URL(url, window.location.origin);
      
      // 只允许相对URL或同域URL
      return urlObj.origin === window.location.origin || 
             url.startsWith('/') || 
             url.startsWith('./') || 
             url.startsWith('../');
    } catch {
      return false;
    }
  }

  /**
   * 验证域名是否合法
   */
  private isValidDomain(hostname: string): boolean {
    const validDomains = [
      'localhost',
      '127.0.0.1',
      'frologi.com',
      'www.frologi.com',
      // 添加其他合法域名
    ];

    return validDomains.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
  }

  /**
   * 验证消息来源是否合法
   */
  private isValidOrigin(origin: string): boolean {
    const validOrigins = [
      window.location.origin,
      'https://frologi.com',
      'https://www.frologi.com',
      // 添加其他合法来源
    ];

    return validOrigins.includes(origin);
  }

  /**
   * 检查消息内容是否可疑
   */
  private isSuspiciousMessage(data: any): boolean {
    if (typeof data === 'string') {
      const suspiciousPatterns = [
        /javascript:/i,
        /data:/i,
        /<script/i,
        /eval\(/i,
        /document\.write/i,
      ];

      return suspiciousPatterns.some(pattern => pattern.test(data));
    }

    return false;
  }

  /**
   * 验证iframe源是否合法
   */
  private isValidIframeSrc(src: string): boolean {
    const allowedIframeSources = [
      'https://www.youtube.com',
      'https://www.googletagmanager.com',
      // 添加其他允许的iframe源
    ];

    return allowedIframeSources.some(source => src.startsWith(source));
  }

  /**
   * 验证脚本源是否合法
   */
  private isValidScriptSrc(src: string): boolean {
    const allowedScriptSources = [
      window.location.origin,
      'https://cdn.jsdelivr.net',
      'https://cdnjs.cloudflare.com',
      'https://hm.baidu.com',
      // 添加其他允许的脚本源
    ];

    return allowedScriptSources.some(source => src.startsWith(source));
  }

  /**
   * 检查是否为可疑导航
   */
  private isSuspiciousNavigation(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // 检查是否跳转到外部恶意域名
      const suspiciousDomains = [
        'phishing-site.com',
        'malware-site.com',
        // 添加已知的恶意域名
      ];

      return suspiciousDomains.some(domain => 
        urlObj.hostname.includes(domain)
      );
    } catch {
      return false;
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.frameCheckInterval) {
      clearInterval(this.frameCheckInterval);
      this.frameCheckInterval = null;
    }

    this.listeners.forEach(cleanup => cleanup());
    this.listeners = [];
    this.isInitialized = false;
  }
}

/**
 * 安全导航工具
 */
export class SecureNavigation {
  /**
   * 安全的页面跳转
   */
  static navigateTo(url: string): boolean {
    try {
      const urlObj = new URL(url, window.location.origin);
      
      // 验证URL安全性
      if (!this.isSecureURL(urlObj)) {
        console.warn(`🚨 阻止跳转到不安全的URL: ${url}`);
        return false;
      }

      // 执行跳转
      window.location.href = urlObj.href;
      return true;
    } catch (error) {
      console.error('导航失败:', error);
      return false;
    }
  }

  /**
   * 验证URL是否安全
   */
  private static isSecureURL(url: URL): boolean {
    // 只允许HTTPS和相对URL
    if (url.protocol !== 'https:' && url.protocol !== 'http:' && url.protocol !== '') {
      return false;
    }

    // 检查域名白名单
    const allowedDomains = [
      window.location.hostname,
      'frologi.com',
      'www.frologi.com',
    ];

    return allowedDomains.some(domain => 
      url.hostname === domain || url.hostname === ''
    );
  }
}

// 自动初始化劫持检测
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    HijackingDetector.getInstance().initialize();
  });
}

export default {
  HijackingDetector,
  SecureNavigation,
};
