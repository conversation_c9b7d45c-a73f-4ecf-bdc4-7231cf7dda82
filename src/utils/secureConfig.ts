/**
 * 安全配置管理工具
 * 处理敏感配置信息的安全存储和访问
 */

import { SecureRandom } from './security';

/**
 * 配置加密工具
 */
export class ConfigEncryption {
  private static readonly ENCRYPTION_KEY = 'app_config_key';

  /**
   * 简单的配置混淆（客户端安全有限，主要用于防止明文暴露）
   */
  static obfuscate(value: string): string {
    const key = this.ENCRYPTION_KEY;
    let result = '';
    
    for (let i = 0; i < value.length; i++) {
      const charCode = value.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    
    return btoa(result); // Base64编码
  }

  /**
   * 解混淆配置
   */
  static deobfuscate(obfuscatedValue: string): string {
    try {
      const decoded = atob(obfuscatedValue);
      const key = this.ENCRYPTION_KEY;
      let result = '';
      
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
        result += String.fromCharCode(charCode);
      }
      
      return result;
    } catch {
      return '';
    }
  }
}

/**
 * 安全的环境配置管理器
 */
export class SecureConfigManager {
  private static instance: SecureConfigManager;
  private config: Map<string, any> = new Map();
  private sensitiveKeys = new Set(['api_key', 'secret', 'token', 'password']);

  private constructor() {
    this.initializeConfig();
  }

  static getInstance(): SecureConfigManager {
    if (!SecureConfigManager.instance) {
      SecureConfigManager.instance = new SecureConfigManager();
    }
    return SecureConfigManager.instance;
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    // 从环境变量加载配置
    this.loadFromEnvironment();
    
    // 从运行时配置加载
    this.loadFromRuntime();
    
    // 设置默认值
    this.setDefaults();
  }

  /**
   * 从环境变量加载配置
   */
  private loadFromEnvironment(): void {
    // 兼容Vite和Next.js的环境变量访问方式
    const getEnvVar = (key: string): string | undefined => {
      // Next.js环境变量
      if (typeof process !== 'undefined' && process.env) {
        return process.env[key];
      }

      // Vite环境变量（仅在客户端可用）
      if (typeof window !== 'undefined' && import.meta?.env) {
        return import.meta.env[key];
      }

      return undefined;
    };

    const envConfig = {
      API_BASE_URL: getEnvVar('VITE_API_BASE_URL') || getEnvVar('NEXT_PUBLIC_API_BASE_URL'),
      APP_BASE_PATH: getEnvVar('VITE_APP_BASE_PATH') || getEnvVar('NEXT_PUBLIC_APP_BASE_PATH'),
      NODE_ENV: getEnvVar('NODE_ENV') || getEnvVar('MODE'),
      USE_MOCK: (getEnvVar('VITE_USE_MOCK') || getEnvVar('NEXT_PUBLIC_USE_MOCK')) === 'true',
    };

    Object.entries(envConfig).forEach(([key, value]) => {
      if (value !== undefined) {
        this.config.set(key, value);
      }
    });
  }

  /**
   * 从运行时配置加载
   */
  private loadFromRuntime(): void {
    if (typeof window !== 'undefined' && window.ENV_CONFIG) {
      Object.entries(window.ENV_CONFIG).forEach(([key, value]) => {
        this.config.set(key, value);
      });
    }
  }

  /**
   * 设置默认配置
   */
  private setDefaults(): void {
    const defaults = {
      API_BASE_URL: '/api',
      APP_BASE_PATH: '/',
      NODE_ENV: 'production',
      USE_MOCK: false,
      REQUEST_TIMEOUT: 10000,
      MAX_RETRY_ATTEMPTS: 3,
      ENABLE_ANALYTICS: true,
      ENABLE_ERROR_REPORTING: true,
    };

    Object.entries(defaults).forEach(([key, value]) => {
      if (!this.config.has(key)) {
        this.config.set(key, value);
      }
    });
  }

  /**
   * 获取配置值
   */
  get<T = any>(key: string, defaultValue?: T): T {
    return this.config.get(key) ?? defaultValue;
  }

  /**
   * 设置配置值
   */
  set(key: string, value: any): void {
    this.config.set(key, value);
  }

  /**
   * 检查是否为敏感配置
   */
  private isSensitive(key: string): boolean {
    return this.sensitiveKeys.has(key.toLowerCase()) || 
           key.toLowerCase().includes('secret') ||
           key.toLowerCase().includes('key') ||
           key.toLowerCase().includes('token');
  }

  /**
   * 获取所有配置（敏感信息会被脱敏）
   */
  getAllConfig(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.config.forEach((value, key) => {
      if (this.isSensitive(key)) {
        result[key] = '***';
      } else {
        result[key] = value;
      }
    });
    
    return result;
  }

  /**
   * 验证配置完整性
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const requiredKeys = ['API_BASE_URL', 'NODE_ENV'];

    requiredKeys.forEach(key => {
      if (!this.config.has(key)) {
        errors.push(`缺少必需的配置项: ${key}`);
      }
    });

    // 验证API_BASE_URL格式
    const apiUrl = this.get('API_BASE_URL');
    if (apiUrl && !apiUrl.startsWith('/') && !apiUrl.startsWith('http')) {
      errors.push('API_BASE_URL格式不正确');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * 第三方服务配置管理
 */
export class ThirdPartyConfigManager {
  private static configs = new Map<string, any>();

  /**
   * 设置EmailJS配置
   */
  static setEmailJSConfig(config: {
    serviceId: string;
    templateId: string;
    publicKey: string;
  }): void {
    // 在生产环境中，这些配置应该从安全的后端API获取
    this.configs.set('emailjs', {
      serviceId: ConfigEncryption.obfuscate(config.serviceId),
      templateId: ConfigEncryption.obfuscate(config.templateId),
      publicKey: ConfigEncryption.obfuscate(config.publicKey),
    });
  }

  /**
   * 获取EmailJS配置
   */
  static getEmailJSConfig(): {
    serviceId: string;
    templateId: string;
    publicKey: string;
  } | null {
    const config = this.configs.get('emailjs');
    if (!config) return null;

    return {
      serviceId: ConfigEncryption.deobfuscate(config.serviceId),
      templateId: ConfigEncryption.deobfuscate(config.templateId),
      publicKey: ConfigEncryption.deobfuscate(config.publicKey),
    };
  }

  /**
   * 设置分析服务配置
   */
  static setAnalyticsConfig(config: {
    trackingId: string;
    apiEndpoint: string;
  }): void {
    this.configs.set('analytics', {
      trackingId: ConfigEncryption.obfuscate(config.trackingId),
      apiEndpoint: config.apiEndpoint, // API端点通常不需要加密
    });
  }

  /**
   * 获取分析服务配置
   */
  static getAnalyticsConfig(): {
    trackingId: string;
    apiEndpoint: string;
  } | null {
    const config = this.configs.get('analytics');
    if (!config) return null;

    return {
      trackingId: ConfigEncryption.deobfuscate(config.trackingId),
      apiEndpoint: config.apiEndpoint,
    };
  }

  /**
   * 清除所有第三方配置
   */
  static clearAllConfigs(): void {
    this.configs.clear();
  }
}

/**
 * 安全的用户ID生成器
 */
export class SecureUserIdGenerator {
  private static readonly USER_ID_KEY = 'secure_user_id';
  private static readonly SESSION_ID_KEY = 'secure_session_id';

  /**
   * 生成或获取安全的用户ID
   */
  static getUserId(): string {
    try {
      let userId = localStorage.getItem(this.USER_ID_KEY);
      
      if (!userId) {
        // 生成更安全的用户ID
        const timestamp = Date.now().toString(36);
        const randomPart = SecureRandom.generateSecureId(16);
        const fingerprint = this.generateFingerprint();
        
        userId = `user_${timestamp}_${randomPart}_${fingerprint}`;
        localStorage.setItem(this.USER_ID_KEY, userId);
      }
      
      return userId;
    } catch {
      // 如果localStorage不可用，生成临时ID
      return `temp_${SecureRandom.generateSecureId(16)}`;
    }
  }

  /**
   * 生成会话ID
   */
  static getSessionId(): string {
    try {
      let sessionId = sessionStorage.getItem(this.SESSION_ID_KEY);
      
      if (!sessionId) {
        sessionId = SecureRandom.generateSessionId();
        sessionStorage.setItem(this.SESSION_ID_KEY, sessionId);
      }
      
      return sessionId;
    } catch {
      return SecureRandom.generateSessionId();
    }
  }

  /**
   * 生成浏览器指纹（简化版）
   */
  private static generateFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
    ].join('|');
    
    // 简单哈希
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 清除用户标识
   */
  static clearUserIdentifiers(): void {
    try {
      localStorage.removeItem(this.USER_ID_KEY);
      sessionStorage.removeItem(this.SESSION_ID_KEY);
    } catch {
      // 忽略错误
    }
  }
}

// 导出单例实例
export const secureConfig = SecureConfigManager.getInstance();

export default {
  SecureConfigManager,
  ThirdPartyConfigManager,
  SecureUserIdGenerator,
  ConfigEncryption,
  secureConfig,
};
