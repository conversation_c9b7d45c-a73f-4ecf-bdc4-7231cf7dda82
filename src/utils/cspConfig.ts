/**
 * 内容安全策略(CSP)配置管理
 * 提供动态CSP配置和nonce生成功能
 */

import { SecureRandom } from './security';

/**
 * CSP指令类型
 */
export interface CSPDirectives {
  'default-src'?: string[];
  'script-src'?: string[];
  'style-src'?: string[];
  'img-src'?: string[];
  'font-src'?: string[];
  'connect-src'?: string[];
  'frame-src'?: string[];
  'object-src'?: string[];
  'media-src'?: string[];
  'child-src'?: string[];
  'worker-src'?: string[];
  'manifest-src'?: string[];
  'base-uri'?: string[];
  'form-action'?: string[];
  'frame-ancestors'?: string[];
  'report-uri'?: string[];
  'report-to'?: string[];
}

/**
 * CSP配置管理器
 */
export class CSPManager {
  private static instance: CSPManager;
  private nonce: string;
  private directives: CSPDirectives;

  private constructor() {
    this.nonce = SecureRandom.generateSecureId(16);
    this.directives = this.getDefaultDirectives();
  }

  static getInstance(): CSPManager {
    if (!CSPManager.instance) {
      CSPManager.instance = new CSPManager();
    }
    return CSPManager.instance;
  }

  /**
   * 获取默认CSP指令
   */
  private getDefaultDirectives(): CSPDirectives {
    return {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        `'nonce-${this.nonce}'`,
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com',
        'https://hm.baidu.com', // 百度统计
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // CSS通常需要内联样式
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com',
        'https://fonts.googleapis.com',
      ],
      'img-src': [
        "'self'",
        'data:',
        'https:',
        'blob:',
      ],
      'font-src': [
        "'self'",
        'https://cdnjs.cloudflare.com',
        'https://fonts.gstatic.com',
        'data:',
      ],
      'connect-src': [
        "'self'",
        'https://api.starrier.org',
        'wss:', // WebSocket连接
      ],
      'frame-src': ["'none'"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
    };
  }

  /**
   * 获取当前nonce值
   */
  public getNonce(): string {
    return this.nonce;
  }

  /**
   * 刷新nonce值
   */
  public refreshNonce(): string {
    this.nonce = SecureRandom.generateSecureId(16);
    this.updateScriptSrcNonce();
    return this.nonce;
  }

  /**
   * 更新script-src中的nonce
   */
  private updateScriptSrcNonce(): void {
    if (this.directives['script-src']) {
      this.directives['script-src'] = this.directives['script-src'].map(src => 
        src.startsWith("'nonce-") ? `'nonce-${this.nonce}'` : src
      );
    }
  }

  /**
   * 添加允许的源
   */
  public addSource(directive: keyof CSPDirectives, source: string): void {
    if (!this.directives[directive]) {
      this.directives[directive] = [];
    }
    
    if (!this.directives[directive]!.includes(source)) {
      this.directives[directive]!.push(source);
    }
  }

  /**
   * 移除允许的源
   */
  public removeSource(directive: keyof CSPDirectives, source: string): void {
    if (this.directives[directive]) {
      this.directives[directive] = this.directives[directive]!.filter(s => s !== source);
    }
  }

  /**
   * 设置指令
   */
  public setDirective(directive: keyof CSPDirectives, sources: string[]): void {
    this.directives[directive] = [...sources];
  }

  /**
   * 获取指令
   */
  public getDirective(directive: keyof CSPDirectives): string[] {
    return this.directives[directive] || [];
  }

  /**
   * 生成CSP字符串
   */
  public generateCSPString(): string {
    const cspParts: string[] = [];
    
    for (const [directive, sources] of Object.entries(this.directives)) {
      if (sources && sources.length > 0) {
        cspParts.push(`${directive} ${sources.join(' ')}`);
      }
    }
    
    return cspParts.join('; ');
  }

  /**
   * 应用CSP到页面
   */
  public applyToPage(): void {
    if (typeof document === 'undefined') return;

    // 移除现有的CSP meta标签
    const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingMeta) {
      existingMeta.remove();
    }

    // 创建新的CSP meta标签
    const meta = document.createElement('meta');
    meta.setAttribute('http-equiv', 'Content-Security-Policy');
    meta.setAttribute('content', this.generateCSPString());
    document.head.appendChild(meta);
  }

  /**
   * 验证URL是否符合CSP策略
   */
  public validateUrl(url: string, directive: keyof CSPDirectives): boolean {
    const sources = this.directives[directive] || [];
    
    // 检查是否允许所有源
    if (sources.includes('*')) return true;
    
    // 检查self
    if (sources.includes("'self'") && this.isSameOrigin(url)) return true;
    
    // 检查具体域名
    try {
      const urlObj = new URL(url);
      const origin = urlObj.origin;
      
      return sources.some(source => {
        if (source.startsWith('https://') || source.startsWith('http://')) {
          return url.startsWith(source) || origin === source;
        }
        if (source === 'https:' && urlObj.protocol === 'https:') return true;
        if (source === 'http:' && urlObj.protocol === 'http:') return true;
        if (source === 'data:' && urlObj.protocol === 'data:') return true;
        if (source === 'blob:' && urlObj.protocol === 'blob:') return true;
        return false;
      });
    } catch {
      return false;
    }
  }

  /**
   * 检查是否为同源URL
   */
  private isSameOrigin(url: string): boolean {
    try {
      const urlObj = new URL(url, window.location.href);
      return urlObj.origin === window.location.origin;
    } catch {
      return false;
    }
  }

  /**
   * 获取开发环境配置
   */
  public getDevConfig(): CSPDirectives {
    return {
      ...this.directives,
      'script-src': [
        "'self'",
        `'nonce-${this.nonce}'`,
        "'unsafe-inline'", // 仅开发环境
        "'unsafe-eval'", // 仅开发环境，用于HMR
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com',
        'https://hm.baidu.com',
        'localhost:*',
        '127.0.0.1:*',
      ],
      'connect-src': [
        "'self'",
        'https://api.starrier.org',
        'ws://localhost:*',
        'wss://localhost:*',
        'http://localhost:*',
        'https://localhost:*',
      ],
    };
  }

  /**
   * 获取生产环境配置
   */
  public getProdConfig(): CSPDirectives {
    return {
      ...this.directives,
      'script-src': [
        "'self'",
        `'nonce-${this.nonce}'`,
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com',
        'https://hm.baidu.com', // 百度统计
      ],
      'style-src': [
        "'self'",
        'https://cdn.jsdelivr.net',
        'https://cdnjs.cloudflare.com',
        'https://fonts.googleapis.com',
      ],
    };
  }

  /**
   * 根据环境应用配置
   */
  public applyEnvironmentConfig(isDevelopment: boolean = false): void {
    this.directives = isDevelopment ? this.getDevConfig() : this.getProdConfig();
    this.applyToPage();
  }

  /**
   * 重置为默认配置
   */
  public reset(): void {
    this.nonce = SecureRandom.generateSecureId(16);
    this.directives = this.getDefaultDirectives();
  }
}

/**
 * 创建带nonce的script标签
 */
export function createNonceScript(content: string): HTMLScriptElement {
  const script = document.createElement('script');
  script.nonce = CSPManager.getInstance().getNonce();
  script.textContent = content;
  return script;
}

/**
 * 验证外部资源URL
 */
export function validateExternalResource(url: string, type: 'script' | 'style' | 'img' | 'font'): boolean {
  const directiveMap = {
    script: 'script-src' as const,
    style: 'style-src' as const,
    img: 'img-src' as const,
    font: 'font-src' as const,
  };
  
  return CSPManager.getInstance().validateUrl(url, directiveMap[type]);
}

// 导出单例实例
export const cspManager = CSPManager.getInstance();

export default {
  CSPManager,
  cspManager,
  createNonceScript,
  validateExternalResource,
};
