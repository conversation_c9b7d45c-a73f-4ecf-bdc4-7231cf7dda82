/**
 * 环境变量兼容工具
 * 完全避免直接访问import.meta，提供Vite和Next.js的统一环境变量访问
 */

// 环境变量缓存
let envCache: Record<string, string | undefined> | null = null;

/**
 * 初始化环境变量缓存
 */
function initEnvCache(): Record<string, string | undefined> {
  if (envCache !== null) {
    return envCache;
  }

  const env: Record<string, string | undefined> = {};

  // 1. 优先使用Next.js的process.env
  if (typeof process !== 'undefined' && process.env) {
    // 复制所有VITE_和NEXT_PUBLIC_前缀的环境变量
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('VITE_') || key.startsWith('NEXT_PUBLIC_') || key === 'NODE_ENV' || key === 'MODE') {
        env[key] = process.env[key];
      }
    });
  }

  // 2. 在客户端，尝试从window对象获取运行时配置
  if (typeof window !== 'undefined') {
    // 从window.ENV_CONFIG获取运行时配置
    const windowEnv = (window as any).ENV_CONFIG;
    if (windowEnv && typeof windowEnv === 'object') {
      Object.assign(env, windowEnv);
    }

    // 从window.__ENV__获取配置（备用方案）
    const windowEnv2 = (window as any).__ENV__;
    if (windowEnv2 && typeof windowEnv2 === 'object') {
      Object.assign(env, windowEnv2);
    }
  }

  // 3. 设置默认值
  env.VITE_API_BASE_URL = env.VITE_API_BASE_URL || env.NEXT_PUBLIC_API_BASE_URL || '/api';
  env.VITE_APP_BASE_PATH = env.VITE_APP_BASE_PATH || env.NEXT_PUBLIC_APP_BASE_PATH || '/';
  env.VITE_USE_MOCK = env.VITE_USE_MOCK || env.NEXT_PUBLIC_USE_MOCK || 'false';
  env.NODE_ENV = env.NODE_ENV || env.MODE || 'production';

  // 缓存结果
  envCache = env;
  return env;
}

/**
 * 获取环境变量值
 * @param key 环境变量键名
 * @param defaultValue 默认值
 * @returns 环境变量值
 */
export function getEnvVar(key: string, defaultValue?: string): string | undefined {
  const env = initEnvCache();
  return env[key] ?? defaultValue;
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return getEnvVar('VITE_API_BASE_URL') || getEnvVar('NEXT_PUBLIC_API_BASE_URL') || '/api';
}

/**
 * 获取应用基础路径
 */
export function getAppBasePath(): string {
  return getEnvVar('VITE_APP_BASE_PATH') || getEnvVar('NEXT_PUBLIC_APP_BASE_PATH') || '/';
}

/**
 * 检查是否启用Mock
 */
export function isMockEnabled(): boolean {
  const mockValue = getEnvVar('VITE_USE_MOCK') || getEnvVar('NEXT_PUBLIC_USE_MOCK');
  return mockValue === 'true';
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  const nodeEnv = getEnvVar('NODE_ENV') || getEnvVar('MODE');
  return nodeEnv === 'development';
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  const nodeEnv = getEnvVar('NODE_ENV') || getEnvVar('MODE');
  return nodeEnv === 'production';
}

/**
 * 获取所有环境变量（调试用）
 */
export function getAllEnvVars(): Record<string, string | undefined> {
  return { ...initEnvCache() };
}

/**
 * 清除环境变量缓存（测试用）
 */
export function clearEnvCache(): void {
  envCache = null;
}

// 导出默认配置对象
export const envConfig = {
  get API_BASE_URL() { return getApiBaseUrl(); },
  get APP_BASE_PATH() { return getAppBasePath(); },
  get USE_MOCK() { return isMockEnabled(); },
  get NODE_ENV() { return getEnvVar('NODE_ENV') || getEnvVar('MODE') || 'production'; },
  get IS_DEV() { return isDevelopment(); },
  get IS_PROD() { return isProduction(); },
};

export default envConfig;
