# 配置文件说明文档

本文档详细说明了项目中各个配置文件的作用和使用方法。

## 📁 配置文件结构

```
frost-chain/
├── config/                     # 配置管理目录
│   └── env.config.cjs          # 统一环境配置管理器
├── scripts/                    # 脚本目录
│   ├── config-manager.sh       # 配置管理脚本
│   ├── secure-build.sh         # 安全构建脚本
│   └── security-check.sh       # 安全检查脚本
├── .env*                       # 环境变量文件
├── docker-compose*.yml         # Docker编排配置
├── *.config.*                  # 各种工具配置
└── docs/                       # 文档目录
    └── CONFIG.md               # 本文档
```

## 🔧 核心配置文件

### 1. 项目配置文件

| 文件名 | 作用 | 状态 | 说明 |
|--------|------|------|------|
| **package.json** | 项目依赖和脚本管理 | ✅ 必需 | 定义项目依赖、脚本命令和元数据 |
| **tsconfig.json** | TypeScript主配置 | ✅ 必需 | TypeScript编译器配置 |
| **tsconfig.node.json** | Node.js环境TS配置 | ✅ 必需 | Vite配置文件的TypeScript配置 |
| **vite.config.ts** | Vite构建配置 | ✅ 必需 | 前端构建工具配置 |
| **tailwind.config.ts** | TailwindCSS配置 | ✅ 必需 | CSS框架配置 |
| **eslint.config.js** | ESLint代码检查配置 | ✅ 必需 | 代码质量和安全检查规则 |

### 2. 环境配置文件

| 文件名 | 作用 | 生成方式 | 说明 |
|--------|------|----------|------|
| **.env.example** | 环境变量模板 | 手动维护 | 提供环境变量示例和说明 |
| **.env** | 默认环境配置 | 自动生成 | 开发环境使用的默认配置 |
| **.env.development** | 开发环境配置 | 自动生成 | 开发环境特定配置 |
| **.env.test** | 测试环境配置 | 自动生成 | 测试环境特定配置 |
| **.env.production** | 生产环境配置 | 自动生成 | 生产环境特定配置 |
| **.env.docker** | Docker环境配置 | 自动生成 | Docker容器环境配置 |
| **.env.local** | 本地环境配置 | 自动生成 | 本地测试环境配置 |

### 3. Docker配置文件

| 文件名 | 作用 | 说明 |
|--------|------|------|
| **Dockerfile** | Docker镜像构建 | 定义如何构建应用的Docker镜像 |
| **docker-compose.yml** | 基础容器编排 | 原有的简单Docker编排配置 |
| **docker-compose.unified.yml** | 统一容器编排 | 新的统一Docker编排配置 |
| **docker-compose.dev.yml** | 开发环境覆盖 | 开发环境特定的Docker配置 |
| **docker-compose.prod.yml** | 生产环境覆盖 | 生产环境特定的Docker配置 |
| **nginx.conf** | Nginx服务器配置 | Web服务器配置，包含安全头设置 |

### 4. 管理脚本

| 文件名 | 作用 | 说明 |
|--------|------|------|
| **config/env.config.cjs** | 环境配置生成器 | 统一管理所有环境的配置 |
| **scripts/config-manager.sh** | 配置管理脚本 | 提供配置文件的生成、验证、清理等功能 |
| **scripts/secure-build.sh** | 安全构建脚本 | 包含安全检查的构建流程 |
| **scripts/security-check.sh** | 安全检查脚本 | 项目安全性检查和评估 |

## 🚀 使用方法

### 配置管理命令

```bash
# 生成环境配置文件
npm run config:generate [环境名]

# 验证配置
npm run config:validate [环境名]

# 清理旧配置文件
npm run config:clean

# 备份当前配置
npm run config:backup

# 列出所有配置文件
npm run config:list

# 初始化配置结构
npm run config:init
```

### Docker部署命令

```bash
# 开发环境
docker-compose -f docker-compose.unified.yml -f docker-compose.dev.yml up

# 生产环境
docker-compose -f docker-compose.unified.yml -f docker-compose.prod.yml up

# 使用环境变量
ENV=dev docker-compose -f docker-compose.unified.yml up
```

### 安全相关命令

```bash
# 安全构建
npm run build:secure

# 安全检查
npm run security:check

# 依赖审计
npm run security:audit
```

## 🔄 配置文件精简对比

### 精简前（17个配置文件）
```
.env.development
.env.test
.env.production
.env.local
.env.docker
.env.example
docker-compose.yml
docker-compose.local.yml
build.sh
+ 8个其他必需配置文件
```

### 精简后（12个配置文件）
```
config/env.config.cjs          # 统一环境配置管理
.env.example                   # 环境变量模板
docker-compose.unified.yml     # 统一Docker配置
docker-compose.dev.yml         # 开发环境覆盖
docker-compose.prod.yml        # 生产环境覆盖
scripts/config-manager.sh      # 配置管理脚本
+ 6个其他必需配置文件
```

### 精简效果
- ✅ **减少了29%的配置文件数量**（17 → 12）
- ✅ **统一了环境配置管理**
- ✅ **简化了Docker配置**
- ✅ **提供了自动化管理工具**
- ✅ **保持了所有原有功能**

## 📝 配置修改指南

### 1. 修改环境变量
```bash
# 编辑统一配置文件
vim config/env.config.cjs

# 重新生成环境文件
npm run config:generate development
```

### 2. 添加新环境
```javascript
// 在 config/env.config.cjs 中添加
const newEnvConfig = {
  ...baseConfig,
  NODE_ENV: 'staging',
  // 其他配置...
};

// 添加到环境映射
const envConfigs = {
  // ...existing configs
  staging: newEnvConfig,
};
```

### 3. 修改Docker配置
```bash
# 编辑统一配置
vim docker-compose.unified.yml

# 或创建新的环境覆盖文件
vim docker-compose.staging.yml
```

## 🔒 安全注意事项

1. **敏感信息**: 不要在配置文件中硬编码敏感信息
2. **环境隔离**: 不同环境使用不同的配置文件
3. **权限控制**: 生产环境配置文件应有适当的访问权限
4. **版本控制**: `.env` 文件不应提交到版本控制系统
5. **定期审查**: 定期检查和更新配置文件

## 🛠️ 故障排除

### 常见问题

1. **配置文件生成失败**
   ```bash
   # 检查Node.js版本
   node --version
   
   # 重新安装依赖
   npm install
   ```

2. **Docker构建失败**
   ```bash
   # 检查Docker配置
   docker-compose config
   
   # 清理Docker缓存
   docker system prune
   ```

3. **环境变量不生效**
   ```bash
   # 验证配置
   npm run config:validate production
   
   # 重新生成配置
   npm run config:generate production
   ```

## 📚 相关文档

- [环境配置详细说明](./ENVIRONMENT.md)
- [Docker部署指南](./DOCKER.md)
- [安全配置指南](./SECURITY.md)
- [开发环境设置](./DEVELOPMENT.md)
